# CDN管理后台

一个基于Vue.js的现代化CDN资源管理系统，集成了文件管理、游戏数据监控和3D模型编辑功能。

## 📋 项目概述

本项目是一个多功能的管理后台系统，主要包含以下核心功能：

- **文件管理**：基于华为云OBS的文件存储和管理
- **游戏管理**：游戏服务器数据监控和玩家管理
- **模型编辑器**：基于Three.js的3D模型编辑工具

## 🛠 技术栈

### 前端框架
- **Vue.js 3.3+** - 渐进式JavaScript框架
- **Vue Router 4.5+** - 官方路由管理器
- **Vite 5.0+** - 现代化构建工具

### UI组件库
- **Element Plus 2.4+** - Vue 3组件库
- **Tailwind CSS 3.4+** - 原子化CSS框架
- **Headless UI** - 无样式组件库
- **Heroicons** - 图标库

### 核心依赖
- **Three.js 0.171+** - 3D图形库
- **Chart.js 4.4+** - 图表库
- **Axios 1.6+** - HTTP客户端
- **esdk-obs-browserjs 3.24+** - 华为云OBS SDK

## 🏗 项目结构

```
cdn/
├── public/                 # 静态资源
│   └── favicon.ico
├── src/
│   ├── components/         # Vue组件
│   │   ├── FileManager/    # 文件管理组件
│   │   ├── GameManager/    # 游戏管理组件
│   │   ├── Layout/         # 布局组件
│   │   └── ModelEditor/    # 模型编辑器组件
│   ├── services/           # 业务服务层
│   │   ├── obsService.js   # OBS文件服务
│   │   ├── gameService.js  # 游戏数据服务
│   │   ├── dbService.js    # 数据库服务
│   │   └── ...
│   ├── utils/              # 工具函数
│   │   ├── obsClient.js    # OBS客户端配置
│   │   └── obsEnv.js       # 环境配置
│   ├── mock/               # 模拟数据
│   ├── router/             # 路由配置
│   ├── assets/             # 静态资源
│   ├── App.vue             # 根组件
│   └── main.js             # 应用入口
├── docs/                   # 项目文档
├── index.html              # HTML模板
├── package.json            # 项目配置
├── vite.config.js          # Vite配置
└── tailwind.config.js      # Tailwind配置
```

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 开发环境启动
```bash
npm run dev
```

访问 http://localhost:3000 查看应用

### 生产构建
```bash
npm run build
```

### 预览构建结果
```bash
npm run preview
```

## 📱 功能模块

### 1. 文件管理模块

**主要功能：**
- 文件/文件夹的上传、下载、删除
- 批量操作支持
- 预签名URL生成
- 文件预览和详情查看
- 目录统计和搜索

**核心组件：**
- `FileList.vue` - 文件列表主界面
- `obsService.js` - OBS服务封装

**技术特点：**
- 支持大文件分片上传
- 智能重试机制
- 缓存优化
- 并发控制

### 2. 游戏管理模块

**主要功能：**
- 游戏服务器状态监控
- 玩家数据统计和图表展示
- 房间管理和详情查看
- 多服务器环境支持

**核心组件：**
- `GameDashboard.vue` - 游戏数据仪表板
- `PlayerList.vue` - 玩家列表
- `PlayersChart.vue` - 玩家数据图表
- `RoomList.vue` - 房间列表
- `RoomDetail.vue` - 房间详情

**技术特点：**
- 实时数据更新
- 多服务器切换
- 数据可视化
- 历史数据存储

### 3. 模型编辑器模块

**主要功能：**
- 3D模型加载和显示
- 场景树管理
- 模型属性编辑
- 交互式3D操作

**核心组件：**
- `ModelEditor.vue` - 模型编辑器主界面
- `SceneTreePanel.vue` - 场景树面板
- `SceneTreeNode.vue` - 场景树节点

**技术特点：**
- 基于Three.js
- 实时渲染
- 交互式编辑
- 场景管理

## ⚙️ 配置说明

### Vite代理配置

项目配置了多个代理路径：

```javascript
// vite.config.js
proxy: {
  '/api': {
    target: 'http://************:5174',  // 正式服API
    changeOrigin: true
  },
  '/api-test': {
    target: 'http://**************:8080', // 测试服API
    changeOrigin: true
  },
  '/satworld-resource': {
    target: 'https://obs.ap-southeast-1.myhuaweicloud.com', // OBS存储
    changeOrigin: true
  }
}
```

### 环境变量

OBS的访问密钥、存储桶和区域等敏感配置通过 `src/utils/obsEnv.js` 进行管理。请根据实际情况在本地环境中配置该文件，但注意不要将其提交到版本控制中。

## 📚 项目文档

本项目的所有详细文档均存放于 `docs/` 目录下，是进行开发、维护和深入了解技术实现的核心参考资料。

- **[API文档](./docs/API文档.md)**：后端服务接口的详细说明。
- **[开发指南](./docs/开发指南.md)**：包含代码规范、开发流程、错误处理等全面的开发指导。
- **性能优化方案**：
  - **[列表查询优化](./docs/README_列表查询优化.md)**：关于异步加载、并发控制和缓存机制的详细说明。
  - **[批量删除优化](./docs/README_批量删除优化.md)**：阐述了并发处理、分批操作和进度反馈的实现。
  - **[GET请求重复问题修复](./docs/README_GET请求重复问题修复.md)**：深度分析并解决GET请求重复的方案。
- **[快速修复指南](./docs/列表查询优化-快速修复.md)**：针对特定问题的快速修复步骤。


## 🐛 常见问题

### 1. OBS文件上传失败
- 检查 `src/utils/obsEnv.js` 中的OBS配置是否正确。
- 确认网络连接状态。
- 查看浏览器控制台的错误信息以获取详细线索。

### 2. 游戏数据无法加载
- 确认API服务器是否正常运行。
- 检查 `vite.config.js` 中的代理配置是否指向正确的服务器地址。
- 验证所使用的API密钥是否有效。

### 3. 3D模型无法显示
- 确认模型文件格式是否受支持（如glTF, GLB）。
- 使用浏览器开发工具检查WebGL支持情况。
- 查看控制台输出，定位Three.js相关的错误。

## 🤝 贡献指南

我们欢迎并鼓励社区贡献。请遵循以下步骤：

1. **Fork** 本项目。
2. 创建您的功能分支 (`git checkout -b feature/AmazingFeature`)。
3. 提交您的更改 (`git commit -m 'Add some AmazingFeature'`)。
4. 推送到分支 (`git push origin feature/AmazingFeature`)。
5. 打开一个 **Pull Request** 进行审核。

## 📄 许可证

本项目为私有项目，版权所有。

## 📞 联系方式

如有问题或建议，请联系开发团队。