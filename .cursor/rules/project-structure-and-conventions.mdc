---
description: 
globs: 
alwaysApply: true
---
# 项目结构与代码规范

本规则文档旨在为项目提供统一的结构指导和编码约定，以提高代码的可读性、可维护性和团队协作效率。

## 项目技术栈

-   **构建工具**: [Vite](mdc:vite.config.js)
-   **UI 框架**: Vue.js (推断自 `App.vue` 和 `main.js`)
-   **CSS 工具**: [Tailwind CSS](mdc:tailwind.config.js), [PostCSS](mdc:postcss.config.js)

## 项目结构

项目遵循标准的 Vue.js 项目结构：

-   **[public/](mdc:public)**: 存放不会被 Webpack 处理的静态资源，会直接被复制到 `dist` 目录。
-   **[src/](mdc:src)**: 存放所有源代码。
    -   **[assets/](mdc:src/assets)**: 存放会被 Webpack 处理的静态资源，如图片、字体和全局样式表。
    -   **[components/](mdc:src/components)**: 存放可复用的Vue组件。
    -   **[router/](mdc:src/router)**: 存放 `vue-router` 的路由配置。
    -   **[services/](mdc:src/services)**: 存放业务逻辑服务和API请求模块。
    -   **[utils/](mdc:src/utils)**: 存放通用工具函数。
    -   **[mock/](mdc:src/mock)**: 存放模拟数据，用于开发和测试。
    -   **[main.js](mdc:src/main.js)**: 应用程序的入口文件，负责初始化Vue实例和插件。
    -   **[App.vue](mdc:src/App.vue)**: 根 Vue 组件。
-   **[package.json](mdc:package.json)**: 项目依赖和脚本配置。
-   **[README.md](mdc:README.md)**: 项目说明文档。

## 代码规范

-   **组件命名**: 组件文件名使用大驼峰命名法 (PascalCase)，例如 `MyComponent.vue`。
-   **函数注释**: 每个函数（包括组件的 `methods`, `computed`, `watch` 等）都必须添加 JSDoc 格式的注释，清晰说明其作用、参数和返回值。
-   **Props 定义**: 组件的 `props` 应该尽可能详细，包含 `type`、`required`、`default` 和 `validator`。
-   **API 请求**: 所有的 API 请求都应该放在 `services/` 目录中，并按模块划分。组件内部不应直接发起 API 请求，应通过调用 service 函数来完成。
-   **环境变量**: 使用 `.env` 文件来管理环境变量，不要将敏感信息硬编码到代码中。

## 大文件模块化处理

为了避免出现单个文件代码量过大的情况，我们约定以下模块化策略：

-   **组件拆分**:
    -   当一个Vue组件的模板或脚本部分变得过大（例如超过300行），应考虑将其拆分为更小的、高内聚的子组件。
    -   将可复用的逻辑提取到 `components/` 目录下的 Dumb/Presentational 组件中。

-   **逻辑提取**:
    -   将复杂的业务逻辑从组件中抽离出来，放到 `services/` 目录中。
    -   将可复用的工具函数或辅助函数放到 `utils/` 目录中。
    -   如果项目状态管理变得复杂，应引入 Vuex 或 Pinia，并将状态管理逻辑放在专门的 `store/` 目录中。

-   **路由模块化**:
    -   如果路由配置 (`router/index.js`) 变得庞大，可以在 `router/` 目录下创建多个文件，按功能模块组织路由，然后在主路由文件中统一导入。



