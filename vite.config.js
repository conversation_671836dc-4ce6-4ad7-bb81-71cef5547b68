import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

export default defineConfig({
  plugins: [vue()],
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://111.91.1.198:5174',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path,
        timeout: 60000, // 增加超时时间到60秒
        proxyTimeout: 60000 // 代理超时时间
      },
      '/api-test': {
        target: 'http://135.181.78.188:8080',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api-test/, ''),
        timeout: 60000, // 增加超时时间到60秒
        proxyTimeout: 60000 // 代理超时时间
      },
      '/satworld-resource': {
        target: 'https://obs.ap-southeast-1.myhuaweicloud.com',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path,
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            // 设置正确的Host头
            proxyReq.setHeader('Host', 'obs.ap-southeast-1.myhuaweicloud.com');
            // 添加必要的CORS头
            proxyReq.setHeader('Access-Control-Allow-Origin', '*');
            proxyReq.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
            proxyReq.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
          });
          proxy.on('proxyRes', (proxyRes, req, res) => {
            // 确保响应包含CORS头
            proxyRes.headers['Access-Control-Allow-Origin'] = '*';
            proxyRes.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS';
            proxyRes.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization';
          });
          proxy.on('error', (err, req, res) => {
            console.error('代理错误:', err);
            res.writeHead(500, {
              'Content-Type': 'text/plain',
              'Access-Control-Allow-Origin': '*'
            });
            res.end('代理服务器错误');
          });
        },
        timeout: 120000,
        proxyTimeout: 120000,
        // 添加重试机制
        retry: 3,
        retryDelay: 10000
      }
    },
    cors: true
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  }
})