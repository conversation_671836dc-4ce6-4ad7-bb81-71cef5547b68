# API 接口文档

本文档详细描述了CDN管理后台系统中各个服务模块的API接口。所有API都应遵循统一的错误响应格式。

## 统一响应格式

### 成功响应
```json
{
  "status": "success",
  "data": { ... }
}
```

### 错误响应
```json
{
  "status": "error",
  "error": {
    "code": "ERROR_CODE",
    "message": "详细的错误信息"
  }
}
```

---

## 📋 目录

- [OBS文件服务 API](#obs文件服务-api)
- [游戏管理服务 API](#游戏管理服务-api)
- [数据库服务 API](#数据库服务-api)
- [模型编辑器服务 API](#模型编辑器服务-api)

## 🗂 OBS文件服务 API

### obsService.js

华为云OBS对象存储服务的封装，提供文件的上传、下载、删除等功能。

#### 1. 列出目录对象

```javascript
/**
 * 列出指定目录下的对象
 * @param {string} prefix - 目录前缀
 * @param {string|null} marker - 分页标记
 * @param {number} maxKeys - 最大返回数量
 * @returns {Promise<ListObjectsResult>} 对象列表结果
 */
async function listObjectsInDirectory(prefix, marker = null, maxKeys = 1000)
```

**响应示例 (成功):**
```json
{
  "status": "success",
  "data": {
    "files": [{
      "key": "models/robot.glb",
      "lastModified": "2023-10-27T10:30:00Z",
      "etag": "\"a1b2c3d4e5f6\"",
      "size": 512000,
      "storageClass": "STANDARD"
    }],
    "folders": [{
      "prefix": "models/archive/"
    }],
    "nextMarker": "some-marker-string"
  }
}
```

#### 2. 生成预签名下载URL

```javascript
/**
 * 生成预签名下载URL
 * @param {string} objectKey - 对象键
 * @param {number} expiresInSeconds - 过期时间（秒）
 * @returns {Promise<Object>} URL生成结果
 */
async function generateSignedDownloadUrl(objectKey, expiresInSeconds = 3600)
```

**响应示例 (成功):**
```json
{
  "status": "success",
  "data": {
    "key": "models/robot.glb",
    "url": "https://obs.ap-southeast-1.myhuaweicloud.com/..."
  }
}
```

#### 3. 批量删除文件

```javascript
/**
 * 批量删除文件
 * @param {string[]} objectKeys - 要删除的对象键数组
 * @param {Function} progressCallback - 进度回调函数
 * @returns {Promise<Object>} 删除结果
 */
async function batchDeleteFiles(objectKeys, progressCallback = null)
```

**响应示例 (成功):**
```json
{
  "status": "success",
  "data": {
    "deleted": ["path/to/file1.txt", "path/to/file2.txt"],
    "errors": []
  }
}
```

**响应示例 (部分失败):**
```json
{
  "status": "success",
  "data": {
    "deleted": ["path/to/file1.txt"],
    "errors": [{
      "key": "path/to/file2.txt",
      "code": "AccessDenied",
      "message": "Access to the object was denied."
    }]
  }
}
```

#### 4. 删除目录

```javascript
/**
 * 删除目录及其所有内容
 * @param {string} directoryPrefix - 目录前缀
 * @param {Function} progressCallback - 进度回调函数
 * @returns {Promise<Object>} 删除结果
 */
async function deleteDirectory(directoryPrefix, progressCallback = null)
```

#### 5. 上传文件

```javascript
/**
 * 上传文件到OBS
 * @param {File} file - 要上传的文件
 * @param {string} objectKey - 对象键
 * @param {Function} progressCallback - 进度回调函数
 * @returns {Promise<Object>} 上传结果
 */
async function uploadFile(file, objectKey, progressCallback = null)
```

## 🎮 游戏管理服务 API

### gameService.js

游戏服务器数据管理和监控功能。

#### 1. 获取服务器状态

```javascript
/**
 * 获取服务器状态信息
 * @param {string} serverType - 服务器类型 ('production'|'test')
 * @returns {Promise<Object>} 服务器状态
 */
async function getServerStatus(serverType = SERVER_TYPE.PRODUCTION)
```
- **HTTP 方法**: `GET`
- **端点路径**: `/api/{serverType}/status`

#### 2. 获取玩家列表

```javascript
/**
 * 获取在线玩家列表
 * @param {string} serverType - 服务器类型
 * @param {Object} options - 查询选项
 * @returns {Promise<Array>} 玩家列表
 */
async function getPlayerList(serverType, options = {})
```
- **HTTP 方法**: `GET`
- **端点路径**: `/api/{serverType}/players`

**查询参数 (`options`):**
| 参数 | 类型 | 描述 | 示例 |
|---|---|---|---|
| `page` | `number` | 页码，从1开始 | `1` |
| `pageSize` | `number` | 每页返回的数量 | `20` |
| `sortBy` | `string` | 排序字段 (如 `level`, `name`) | `level` |
| `sortOrder` | `string` | 排序顺序 (`asc` 或 `desc`) | `desc` |
| `search` | `string` | 搜索关键词，模糊匹配玩家名 | `John` |

**响应示例 (成功):**
```json
{
  "status": "success",
  "data": {
    "players": [
      { "id": "p1", "name": "PlayerOne", "level": 99 },
      { "id": "p2", "name": "PlayerTwo", "level": 98 }
    ],
    "pagination": {
      "total": 150,
      "page": 1,
      "pageSize": 20,
      "totalPages": 8
    }
  }
}
```

#### 3. 获取房间列表

```javascript
/**
 * 获取游戏房间列表
 * @param {string} serverType - 服务器类型
 * @returns {Promise<Array>} 房间列表
 */
async function getRoomList(serverType)
```
- **HTTP 方法**: `GET`
- **端点路径**: `/api/{serverType}/rooms`

#### 4. 获取房间详情

```javascript
/**
 * 获取房间详细信息
 * @param {string} serverType - 服务器类型
 * @param {string} roomId - 房间ID
 * @returns {Promise<Object>} 房间详情
 */
async function getRoomDetail(serverType, roomId)
```
- **HTTP 方法**: `GET`
- **端点路径**: `/api/{serverType}/rooms/{roomId}`

#### 5. 获取玩家统计数据

```javascript
/**
 * 获取玩家统计数据（用于图表显示）
 * @param {string} serverType - 服务器类型
 * @param {string} timeRange - 时间范围
 * @returns {Promise<Object>} 统计数据
 */
async function getPlayerStats(serverType, timeRange = '24h')
```
- **HTTP 方法**: `GET`
- **端点路径**: `/api/{serverType}/stats/players`
- **查询参数**: `range={timeRange}` (e.g., `range=24h`)

## 🗄 数据库服务 API

### dbService.js

本地数据库存储服务，用于缓存和历史数据管理。

#### 1. 保存游戏数据

```javascript
/**
 * 保存游戏数据到本地数据库
 * @param {string} serverType - 服务器类型
 * @param {Object} data - 游戏数据
 * @returns {Promise<void>}
 */
async function saveGameData(serverType, data)
```

#### 2. 获取历史数据

```javascript
/**
 * 获取历史游戏数据
 * @param {string} serverType - 服务器类型
 * @param {string} timeRange - 时间范围
 * @returns {Promise<Array>} 历史数据
 */
async function getHistoryData(serverType, timeRange)
```

#### 3. 清理过期数据

```javascript
/**
 * 清理过期的历史数据
 * @param {number} daysToKeep - 保留天数
 * @returns {Promise<void>}
 */
async function cleanupExpiredData(daysToKeep = 30)
```

## 🎨 模型编辑器服务 API

### modelEditorService.ts

3D模型编辑和场景管理功能。

#### 1. 加载3D模型

```typescript
/**
 * 加载3D模型文件
 * @param {string} modelUrl - 模型文件URL
 * @param {string} format - 模型格式
 * @returns {Promise<Object3D>} 3D对象
 */
async function loadModel(modelUrl: string, format: string): Promise<Object3D>
```

#### 2. 创建场景

```typescript
/**
 * 创建新的3D场景
 * @param {HTMLElement} container - 容器元素
 * @returns {Scene} Three.js场景对象
 */
function createScene(container: HTMLElement): Scene
```

#### 3. 添加光源

```typescript
/**
 * 添加光源到场景
 * @param {Scene} scene - 场景对象
 * @param {string} lightType - 光源类型
 * @param {Object} options - 光源选项
 * @returns {Light} 光源对象
 */
function addLight(scene: Scene, lightType: string, options: Object): Light
```

#### 4. 导出场景

```typescript
/**
 * 导出场景数据
 * @param {Scene} scene - 场景对象
 * @param {string} format - 导出格式
 * @returns {Promise<Blob>} 导出数据
 */
async function exportScene(scene: Scene, format: string): Promise<Blob>
```

## 🔧 工具函数 API

### obsClient.js

OBS客户端配置和初始化。

```javascript
/**
 * 创建OBS客户端实例
 * @returns {ObsClient} OBS客户端
 */
function createObsClient(): ObsClient

/**
 * 获取OBS配置
 * @returns {Object} 配置对象
 */
function getObsConfig(): Object
```

### obsEnv.js

环境配置管理。

```javascript
/**
 * OBS配置常量
 */
export const OBS_CONFIG = {
  accessKeyId: string,
  secretAccessKey: string,
  server: string,
  signature: string
}
```

## 📝 错误处理

所有API都遵循统一的错误处理规范：

### 错误响应格式

```javascript
{
  status: 'failure',
  error: string,        // 错误描述
  code?: string,        // 错误代码
  details?: Object      // 详细错误信息
}
```

### 常见错误代码

- `NETWORK_ERROR` - 网络连接错误
- `AUTH_ERROR` - 认证失败
- `PERMISSION_DENIED` - 权限不足
- `FILE_NOT_FOUND` - 文件不存在
- `INVALID_PARAMETER` - 参数无效
- `QUOTA_EXCEEDED` - 配额超限
- `SERVER_ERROR` - 服务器内部错误

## 🔄 重试机制

所有网络请求都实现了智能重试机制：

```javascript
/**
 * 通用重试函数
 * @param {Function} fn - 要重试的函数
 * @param {number} retries - 重试次数
 * @param {number} delay - 延迟时间
 * @param {string} operationName - 操作名称
 */
async function retry(fn, retries = 3, delay = 1000, operationName = '操作')
```

**重试策略：**
- 默认重试3次
- 指数退避延迟（1s, 2s, 4s...）
- 最大延迟30秒
- 详细的错误日志记录

## 📊 性能优化

### 缓存机制

1. **URL缓存**
   - 预签名URL自动缓存
   - 过期时间管理
   - 内存缓存清理

2. **数据缓存**
   - 游戏数据本地缓存
   - 智能缓存更新
   - 缓存失效策略

### 并发控制

1. **批量操作**
   - 默认并发数：3
   - 可配置并发限制
   - 队列管理

2. **请求限流**
   - 防止API过载
   - 智能延迟机制
   - 优雅降级

## 🔐 安全考虑

1. **认证机制**
   - API密钥管理
   - 签名验证
   - 权限控制

2. **数据保护**
   - 敏感信息加密
   - 安全传输
   - 访问日志记录

## 📈 监控和日志

1. **操作日志**
   - 详细的操作记录
   - 错误追踪
   - 性能监控

2. **统计信息**
   - API调用统计
   - 性能指标
   - 错误率监控

---

更多详细信息请参考源代码注释和相关文档。