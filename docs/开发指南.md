# 开发指南

本指南旨在帮助新开发者快速上手CDN管理后台系统的开发工作。

## 📋 目录

- [开发环境搭建](#开发环境搭建)
- [项目结构详解](#项目结构详解)
- [开发规范](#开发规范)
- [调试指南](#调试指南)
- [性能优化](#性能优化)
- [常见问题](#常见问题)
- [部署指南](#部署指南)

## 🛠 开发环境搭建

### 系统要求

- **Node.js**: >= 16.0.0
- **npm**: >= 8.0.0 或 **yarn**: >= 1.22.0
- **Git**: 最新版本
- **VS Code**: 推荐IDE（可选）

### 环境配置

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd cdn
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **环境变量配置**
   
   在项目根目录下创建 `.env.local` 文件，用于存放本地开发环境的敏感信息和配置。此文件**不应**提交到版本控制中。

   ```env
   # .env.local

   # Vite 端口配置
   VITE_DEV_PORT=3000

   # API 超时配置 (毫秒)
   VITE_API_TIMEOUT=30000
   
   # OBS 服务配置
   # 请从华为云获取你的 Access Key ID 和 Secret Access Key
   VITE_OBS_ACCESS_KEY_ID="your_access_key_id"
   VITE_OBS_SECRET_ACCESS_KEY="your_secret_access_key"
   VITE_OBS_SERVER="https://your-obs-server-endpoint" # 例如：obs.ap-southeast-1.myhuaweicloud.com
   VITE_OBS_BUCKET_NAME="your-bucket-name"
   ```
   *注意：项目中的 `src/utils/obsEnv.js` 模块用于读取和处理这些环境变量，请不要直接在该文件中硬编码密钥。*

4. **启动开发服务器**
   ```bash
   npm run dev
   ```

### VS Code 推荐插件

为了获得最佳的开发体验，建议安装以下 VS Code 插件：

```json
{
  "recommendations": [
    "vue.volar",
    "vue.vscode-typescript-vue-plugin",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next"
  ]
}
```

## 🏗 项目结构详解

### 核心目录结构

```
src/
├── components/          # 组件目录
│   ├── FileManager/     # 文件管理组件
│   │   ├── FileList.vue           # 文件列表主组件
│   │   ├── FileUpload.vue         # 文件上传组件
│   │   ├── FilePreview.vue        # 文件预览组件
│   │   └── BatchOperations.vue    # 批量操作组件
│   ├── GameManager/     # 游戏管理组件
│   │   ├── GameDashboard.vue      # 游戏仪表板
│   │   ├── PlayerList.vue         # 玩家列表
│   │   ├── RoomList.vue           # 房间列表
│   │   └── ServerStatus.vue       # 服务器状态
│   ├── Layout/          # 布局组件
│   │   ├── AppLayout.vue          # 主布局
│   │   ├── Sidebar.vue            # 侧边栏
│   │   └── Header.vue             # 顶部导航
│   └── ModelEditor/     # 模型编辑器组件
│       ├── ModelEditor.vue        # 主编辑器
│       └── ...
├── services/            # 服务层 (业务逻辑与API封装)
│   ├── obsService.js              # OBS文件服务封装
│   ├── gameService.js             # 游戏管理服务
│   └── dbService.js               # 数据库服务
├── utils/               # 工具函数
│   ├── obsClient.js               # OBS客户端配置与实例化
│   ├── obsEnv.js                  # 环境变量读取与管理
│   ├── formatters.js              # 格式化工具
│   └── ...
├── router/              # 路由配置
│   └── index.js                   # 路由定义
├── assets/              # 静态资源 (图片、图标等)
└── ...
```

### 组件设计原则

#### 1. 单一职责原则
每个组件只负责一个特定的功能：

```vue
<!-- ✅ 好的例子：专注于文件上传 -->
<template>
  <div class="file-upload">
    <!-- 上传相关UI -->
  </div>
</template>

<script>
export default {
  name: 'FileUpload',
  // 只处理上传逻辑
}
</script>
```

#### 2. 组件通信规范

**父子组件通信：**
```vue
<!-- 父组件 -->
<template>
  <FileUpload 
    :max-size="maxFileSize"
    @upload-success="handleUploadSuccess"
    @upload-error="handleUploadError"
  />
</template>

<!-- 子组件 -->
<script>
export default {
  props: {
    maxSize: {
      type: Number,
      default: 10 * 1024 * 1024 // 10MB
    }
  },
  emits: ['upload-success', 'upload-error'],
  methods: {
    handleUpload() {
      // 上传逻辑
      this.$emit('upload-success', result)
    }
  }
}
</script>
```

**兄弟组件通信：**
```javascript
// 使用事件总线或状态管理
import { EventBus } from '@/utils/eventBus'

// 发送事件
EventBus.emit('file-selected', fileInfo)

// 监听事件
EventBus.on('file-selected', this.handleFileSelected)
```

## 📝 开发规范

### 代码风格

#### 1. 命名规范

```javascript
// ✅ 组件名：PascalCase
const FileUpload = {}
const GameDashboard = {}

// ✅ 变量名：camelCase
const fileList = []
const uploadProgress = 0

// ✅ 常量：UPPER_SNAKE_CASE
const MAX_FILE_SIZE = 10 * 1024 * 1024
const API_ENDPOINTS = {}

// ✅ 文件名：kebab-case
// file-upload.vue
// game-dashboard.vue

// ✅ CSS类名：BEM (Block Element Modifier) 或原子化CSS
.file-upload {}
.file-upload__button--disabled {}
```

#### 2. Vue组件结构

推荐遵循 Vue 官方风格指南。组件脚本部分的结构建议如下：

```vue
<script>
// 1. 导入 (外部库 > 内部模块)
import { ref, computed, onMounted } from 'vue'
import obsService from '@/services/obsService'
import SomeComponent from './SomeComponent.vue'

// 2. 组件定义
export default {
  name: 'ComponentName', // PascalCase, 与文件名一致
  
  // 3. 组件选项 (按推荐顺序排列)
  components: {
    SomeComponent
  },
  props: {
    // ...
  },
  emits: ['custom-event'],
  
  // 4. setup 函数
  setup(props, { emit }) {
    // 响应式状态
    const myRef = ref('')

    // 计算属性
    const myComputed = computed(() => {
      return myRef.value + '!'
    })

    // 方法
    const myMethod = () => {
      emit('custom-event')
    }

    // 生命周期钩子
    onMounted(() => {
      // ...
    })

    // 返回暴露给模板的值
    return {
      myRef,
      myComputed,
      myMethod
    }
  }
}
</script>
```

### 函数注释规范

所有函数，特别是服务层和工具函数，都必须添加符合 JSDoc 规范的注释。

```javascript
/**
 * 上传文件到OBS
 * @param {File} file - 需要上传的文件对象
 * @param {object} options - 上传选项
 * @param {string} options.path - 文件在存储桶中的路径
 * @param {function} options.onProgress - 上传进度回调函数
 * @returns {Promise<object>} 返回包含文件URL和信息的Promise
 * @throws {Error} 如果上传失败，则抛出错误
 */
async function uploadFileToOBS(file, options) {
  try {
    // ...上传逻辑
  } catch (error) {
    console.error('OBS Upload Failed:', error);
    throw new Error('文件上传失败，请稍后重试。');
  }
}
```

### 错误处理

- **必须**在所有可能发生异常的操作（如API请求、文件操作）外层使用 `try...catch` 块进行包裹。
- **必须**在 `catch` 块中记录详细的错误信息到控制台，以便调试。
- 向用户显示的错误信息应该是友好且不暴露实现细节的。
- 在服务层中，API 请求失败时应抛出统一格式的错误，方便上层组件捕获和处理。

## 🐛 调试指南

### 1. 开发工具配置

#### Vue DevTools
安装Vue DevTools浏览器扩展，用于调试Vue组件状态。

#### 控制台调试
```javascript
// 开发环境下的调试日志
if (import.meta.env.DEV) {
  console.log('调试信息:', data)
  console.table(arrayData)
  console.group('API调用')
  console.log('请求参数:', params)
  console.log('响应数据:', response)
  console.groupEnd()
}
```

### 2. 网络请求调试

#### 代理配置调试
```javascript
// vite.config.js
export default {
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('代理请求:', req.method, req.url)
          })
          
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('代理响应:', proxyRes.statusCode, req.url)
          })
        }
      }
    }
  }
}
```

### 3. 性能调试

#### 组件性能监控
```vue
<script>
import { ref, onMounted, onUpdated } from 'vue'

export default {
  setup() {
    const renderCount = ref(0)
    
    onMounted(() => {
      console.log('组件挂载时间:', performance.now())
    })
    
    onUpdated(() => {
      renderCount.value++
      console.log(`组件更新次数: ${renderCount.value}`)
    })
    
    return { renderCount }
  }
}
</script>
```

## ⚡ 性能优化

### 列表查询优化

**问题**：文件列表加载缓慢，特别是包含大量子目录的情况。

**解决方案**：
1. **异步加载**：主列表和目录统计分离
2. **并发控制**：使用 RequestManager 避免重复请求
3. **缓存机制**：智能缓存减少API调用

**实现代码**：
```javascript
// RequestManager 实现
class RequestManager {
  constructor() {
    this.pendingRequests = new Map();
    this.requestCache = new Map();
    this.cacheTimeout = 5000;
  }

  async request(key, requestFn) {
    // 检查缓存
    const cached = this.requestCache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }

    if (this.pendingRequests.has(key)) {
      return this.pendingRequests.get(key);
    }

    const promise = requestFn().then(result => {
      this.requestCache.set(key, {
        data: result,
        timestamp: Date.now()
      });
      return result;
    }).finally(() => {
      this.pendingRequests.delete(key);
    });

    this.pendingRequests.set(key, promise);
    return promise;
  }
}
```

### GET 请求重复问题修复

**问题**：文件列表加载时出现大量重复的 GET 请求，影响性能和用户体验。

**解决方案**：
1. **代理配置优化**：精确匹配、CORS 处理、重试机制
2. **缓存机制增强**：5秒缓存、错误处理、定期清理
3. **OBS 客户端优化**：连接池、长连接、重试配置
4. **服务层改进**：统一重试、日志记录、错误处理

**关键修复点**：
```javascript
// 1. Vite 代理配置优化
'/satworld-resource': {
  target: 'https://satworld-resource.obs.cn-east-3.myhuaweicloud.com',
  changeOrigin: true,
  secure: true,
  timeout: 120000,
  retry: 3,
  retryDelay: 1000
}

// 2. RequestManager 缓存增强
class RequestManager {
  constructor() {
    this.requestCache = new Map();
    this.cacheTimeout = 5000; // 5秒缓存
  }
  
  cleanExpiredCache() {
    const now = Date.now();
    for (const [key, value] of this.requestCache.entries()) {
      if (now - value.timestamp >= this.cacheTimeout) {
        this.requestCache.delete(key);
      }
    }
  }
}

// 3. OBS 客户端配置优化
new ObsClient({
  max_retry_count: 3,
  retry_delay: 1000,
  max_connections: 10,
  keep_alive: true
});
```

### 1. 组件优化

#### 懒加载
```javascript
// router/index.js
const routes = [
  {
    path: '/game',
    component: () => import('@/components/GameManager/GameDashboard.vue')
  },
  {
    path: '/editor',
    component: () => import('@/components/ModelEditor/ModelEditor.vue')
  }
]
```

#### 虚拟滚动
```vue
<template>
  <el-virtual-list
    :data="fileList"
    :height="400"
    :item-size="50"
  >
    <template #default="{ item }">
      <FileItem :file="item" />
    </template>
  </el-virtual-list>
</template>
```

### 2. 数据优化

#### 分页加载
```javascript
const useFileList = () => {
  const fileList = ref([])
  const loading = ref(false)
  const hasMore = ref(true)
  const currentPage = ref(1)
  const pageSize = 50
  
  const loadMore = async () => {
    if (loading.value || !hasMore.value) return
    
    loading.value = true
    
    try {
      const data = await obsService.listObjects({
        page: currentPage.value,
        pageSize
      })
      
      fileList.value.push(...data.files)
      hasMore.value = data.hasMore
      currentPage.value++
      
    } finally {
      loading.value = false
    }
  }
  
  return {
    fileList,
    loading,
    hasMore,
    loadMore
  }
}
```

#### 缓存策略
```javascript
// utils/cache.js
class Cache {
  constructor(maxSize = 100) {
    this.cache = new Map()
    this.maxSize = maxSize
  }
  
  get(key) {
    if (this.cache.has(key)) {
      // LRU: 移到最后
      const value = this.cache.get(key)
      this.cache.delete(key)
      this.cache.set(key, value)
      return value
    }
    return null
  }
  
  set(key, value) {
    if (this.cache.has(key)) {
      this.cache.delete(key)
    } else if (this.cache.size >= this.maxSize) {
      // 删除最旧的项
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }
    
    this.cache.set(key, value)
  }
}

// 使用缓存
const fileCache = new Cache(50)

async function getFileInfo(fileKey) {
  const cached = fileCache.get(fileKey)
  if (cached) {
    return cached
  }
  
  const fileInfo = await obsService.getFileInfo(fileKey)
  fileCache.set(fileKey, fileInfo)
  return fileInfo
}
```

### 3. 打包优化

#### 代码分割
```javascript
// vite.config.js
export default {
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'element-plus': ['element-plus'],
          'three': ['three'],
          'obs-sdk': ['esdk-obs-nodejs']
        }
      }
    }
  }
}
```

## ❓ 常见问题

### 1. OBS相关问题

**Q: 预签名URL生成失败**
```javascript
// 检查配置
console.log('OBS配置:', {
  accessKeyId: process.env.VITE_OBS_ACCESS_KEY_ID?.substring(0, 8) + '...',
  server: process.env.VITE_OBS_SERVER
})

// 检查网络连接
try {
  const response = await fetch(process.env.VITE_OBS_SERVER)
  console.log('OBS服务器连接状态:', response.status)
} catch (error) {
  console.error('无法连接到OBS服务器:', error)
}
```

**Q: 文件上传进度不更新**
```javascript
// 确保正确绑定进度回调
const uploadFile = (file) => {
  return obsService.uploadFile(file, {
    onProgress: (progress) => {
      console.log('上传进度:', progress)
      // 更新UI
      uploadProgress.value = progress
    }
  })
}
```

### 2. 路由问题

**Q: 页面刷新后404**
```javascript
// 确保服务器配置支持SPA
// nginx配置示例
/*
location / {
  try_files $uri $uri/ /index.html;
}
*/

// 或使用hash模式
const router = createRouter({
  history: createWebHashHistory(),
  routes
})
```

### 3. 样式问题

**Q: Element Plus样式不生效**
```javascript
// main.js
import 'element-plus/dist/index.css'
import ElementPlus from 'element-plus'

app.use(ElementPlus)
```

## 🚀 部署指南

### 1. 构建项目

```bash
# 生产环境构建
npm run build

# 预览构建结果
npm run preview
```

### 2. 环境配置

#### 生产环境变量
```env
# .env.production
VITE_API_BASE_URL=https://api.yourserver.com
VITE_OBS_SERVER=https://obs.yourserver.com
VITE_NODE_ENV=production
```

### 3. 服务器配置

#### Nginx配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    root /var/www/cdn/dist;
    index index.html;
    
    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://backend-server:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### 4. Docker部署

```dockerfile
# Dockerfile
FROM node:16-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

```bash
# 构建和运行
docker build -t cdn-admin .
docker run -p 80:80 cdn-admin
```

---

## 📚 相关资源

- [Vue 3 官方文档](https://vuejs.org/)
- [Element Plus 文档](https://element-plus.org/)
- [Vite 文档](https://vitejs.dev/)
- [华为云OBS文档](https://support.huaweicloud.com/obs/)

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📞 技术支持

如有问题，请通过以下方式联系：
- 创建Issue
- 发送邮件至：<EMAIL>
- 技术交流群：[群号]

---

**祝您开发愉快！** 🎉