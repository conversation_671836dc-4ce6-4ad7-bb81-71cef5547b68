{"name": "cdn-manager", "version": "1.0.0", "private": true, "scripts": {"dev": "vite --host 0.0.0.0", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@headlessui/vue": "^1.7.0", "@heroicons/vue": "^2.0.0", "axios": "^1.6.0", "chart.js": "^4.4.9", "element-plus": "^2.4.0", "esdk-obs-browserjs": "^3.24.3", "three": "^0.171.0", "vue": "^3.3.0", "vue-router": "^4.5.1"}, "devDependencies": {"@tailwindcss/forms": "^0.5.0", "@types/three": "^0.171.0", "@vitejs/plugin-vue": "^4.5.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "tailwindcss": "^3.4.0", "vite": "^5.0.0", "esdk-obs-browserjs": "3.24.3"}}