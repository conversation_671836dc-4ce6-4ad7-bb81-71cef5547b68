import ObsClient from 'esdk-obs-browserjs';
import { OBS_CONFIG } from './obsEnv';

// // 在你的应用入口文件 (main.js) 或 obsClient.js 顶部添加:
// const originalXHROpen = XMLHttpRequest.prototype.open;
// XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
//   console.log('[XHR Intercept] Method:', method, 'URL:', url); // 打印 URL
//   // 如果 URL 看起来有问题，可以在这里抛出错误或打断点
//   if (typeof url === 'string' && (url.startsWith('/') && !url.startsWith('//') && !url.includes(window.location.host))) {
//      console.error('[XHR Intercept] Potentially problematic relative URL:', url);
//      // debugger; // 取消注释以在此处暂停
//   }
//   return originalXHROpen.apply(this, arguments);
// }; 

// 单例模式：缓存客户端实例
let obsClientInstance = null;
let isInitialized = false;

/**
 * @description 初始化并返回华为云OBS客户端实例（单例模式）。
 *              使用Vite代理后的路径作为server地址。
 * @returns {ObsClient} OBS客户端实例。
 * @throws {Error} 如果AK或SK未配置，则抛出错误。
 */
const getObsClient = () => {
  // 如果已经初始化过，直接返回缓存的实例
  if (obsClientInstance) {
    return obsClientInstance;
  }

  // 从环境变量或配置文件中获取AK和SK
  const accessKeyId = OBS_CONFIG.accessKey;
  const secretAccessKey = OBS_CONFIG.secretKey;
  const server = `${window.location.protocol}//${window.location.host}`;

  if (!accessKeyId || !secretAccessKey || accessKeyId === 'YOUR_ACCESS_KEY' || secretAccessKey === 'YOUR_SECRET_KEY') {
    const errorMsg = '华为云OBS的Access Key ID (AK) 或 Secret Access Key (SK) 未正确配置，请检查 src/utils/obsEnv.js 文件。';
    console.error(errorMsg);
    throw new Error(errorMsg);
  }

  try {
    obsClientInstance = new ObsClient({
      access_key_id: accessKeyId,
      secret_access_key: secretAccessKey,
      server: server,
      path_style: true,
      timeout: 120000,
      max_retry_count: 3,
      retry_delay: 1000,
      // 添加更多配置选项
      is_signature_negotiation: false,
      max_connections: 10,
      // 启用长连接
      keep_alive: true,
      // 设置用户代理
      user_agent: 'CDN-Manager/1.0.0'
    });
    
    // 只在第一次初始化时打印日志
    if (!isInitialized) {
      console.log('华为云OBS客户端初始化成功。');
      console.log('服务器地址:', server);
      console.log('使用路径样式访问:', true);
      isInitialized = true;
    }
    
    return obsClientInstance;
  } catch (error) {
    console.error('华为云OBS客户端初始化失败:', error);
    // 重置实例以便下次重试
    obsClientInstance = null;
    throw error;
  }
};

// 导出单个客户端实例，或者一个获取实例的函数，根据您的使用场景选择
// 如果希望在应用中共享同一个实例，可以直接导出实例：
// export const obsClient = getObsClient();

// 如果希望每次调用都获取新的实例（一般不推荐，除非有特殊需求），或者延迟初始化：
export default getObsClient;

/**
 * JSDoc for OBS_CONFIG to satisfy linter or type checker if needed.
 * @typedef {import('./obsEnv').OBS_CONFIG} OBS_CONFIG
 */
