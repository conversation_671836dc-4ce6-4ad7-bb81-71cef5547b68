import getObsClient from '../utils/obsClient';
import { OBS_CONFIG } from '../utils/obsEnv';

/**
 * 通用的异步函数重试辅助方法。
 * @async
 * @param {function(): Promise<any>} fn - 需要执行并可能重试的异步函数。
 * @param {number} [retries=3] - 最大重试次数。
 * @param {number} [delay=1000] - 初始重试延迟时间（毫秒）。
 * @param {string} [operationName='操作'] - 操作的名称，用于日志输出。
 * @returns {Promise<any>} 异步函数的执行结果。
 * @throws {Error} 如果所有重试均失败，则抛出最后一次尝试的错误。
 */
async function retry(fn, retries = 3, delay = 1000, operationName = '操作') {
  try {
    return await fn();
  } catch (error) {
    if (retries <= 0) {
      console.error(`${operationName}失败，已达最大重试次数。最终错误:`, error);
      throw error;
    }
    console.warn(`${operationName}发生错误，将在 ${delay}ms 后重试 (${retries} 次剩余)。错误:`, error.message);
    await new Promise(resolve => setTimeout(resolve, delay));
    // 指数退避增加下一次延迟，但可以增加一个最大延迟上限避免过长等待
    return retry(fn, retries - 1, Math.min(delay * 2, 30000), operationName); // 例如，最大延迟30秒
  }
}

// 预签名 URL 缓存
const urlCache = new Map();

/**
 * @typedef {object} ListedObject
 * @property {string} key - 对象键 (完整路径)
 * @property {string} lastModified - 最后修改时间
 * @property {string} etag - 对象的ETag
 * @property {number} size - 对象大小 (字节)
 * @property {string} storageClass - 存储类别
 */

/**
 * @typedef {object} ListedPrefix
 * @property {string} prefix - 公共前缀 (模拟文件夹)
 */

/**
 * @typedef {object} ListObjectsResult
 * @property {ListedObject[]} files - 文件列表
 * @property {ListedPrefix[]} folders - 文件夹列表 (公共前缀)
 * @property {string|null} nextMarker - 用于下一页查询的标记，如果没有更多则为null
 * @property {string} currentPrefix - 当前查询的前缀
 */

/**
 * @typedef {object} CopyStatus
 * @property {string} sourceKey - 源对象的键
 * @property {string} destinationKey - 目标对象的键
 * @property {'success' | 'failure'} status - 复制状态
 * @property {string} [error] - 如果复制失败，则为错误信息
 */

/**
 * @typedef {object} DeleteError
 * @property {string} Key - 删除失败的对象的Key
 * @property {string} Code - 错误码
 * @property {string} Message - 错误信息
 */

/**
 * @typedef {object} BatchDeleteResult
 * @property {string[]} deleted - 成功删除的对象Key列表。
 * @property {DeleteError[]} errors - 删除失败的对象列表及其错误信息。
 * @property {string} prefix - 执行删除操作的原始前缀。
 * @property {number} totalAttempted - 尝试删除的对象总数。
 * @property {'success' | 'partial_success' | 'failure'} overallStatus - 整体删除状态。
 * @property {string} [message] - 关于整体操作的附加消息。
 */

/**
 * 列出OBS中指定前缀（目录）下的对象（文件和文件夹）。
 * @async
 * @param {string} [prefix=''] - 要列出的对象的前缀（例如 'images/' 或 'documents/subfolder/'）。默认为空字符串，表示根目录。
 * @param {string} [marker=null] - 上一次列举操作返回的 `NextMarker` 值，用于获取下一页结果。首次调用时省略或传入null。
 * @param {number} [maxKeys=1000] - 单次请求返回的最大对象数量，默认为1000。
 * @returns {Promise<ListObjectsResult>} 包含文件、文件夹和下一个分页标记的对象。
 * @throws {Error} 当OBS SDK调用失败时抛出错误。
 */
export async function listObjectsInDirectory(prefix = 'website_public', marker = null, maxKeys = 1000, signal = null) {
  const obsClient = getObsClient();
  const bucketName = OBS_CONFIG.bucket;

  if (!bucketName) {
    const errorMsg = 'OBS Bucket 名称未在 obsEnv.js 中配置。请检查 OBS_CONFIG.bucket。';
    console.error(errorMsg);
    throw new Error(errorMsg);
  }

  const params = {
    Bucket: bucketName,
    Prefix: prefix,
    MaxKeys: maxKeys,
    Delimiter: '/', // 使用 '/' 作为分隔符来模拟文件夹结构
  };

  if (marker) {
    params.Marker = marker;
  }

  try {
    // 检查是否被取消
    if (signal && signal.aborted) {
      throw new Error('操作被取消');
    }

    // 只在首次列出时打印日志
    if (!marker) {
      console.log(`[listObjects] 列出目录: ${prefix || 'website_public'}`);
      console.log(`[listObjects] 请求参数:`, JSON.stringify(params, null, 2));
    }

    // 使用重试机制调用OBS API
    const result = await retry(
      () => obsClient.listObjects(params),
      3,
      1000,
      `列出目录 ${prefix || 'website_public'}`
    );

    if (result.CommonMsg.Status < 300) {
      const files = result.InterfaceResult.Contents ? result.InterfaceResult.Contents.map(item => ({
        key: item.Key,
        lastModified: item.LastModified,
        etag: item.ETag,
        size: item.Size,
        storageClass: item.StorageClass,
      })) : [];

      const folders = result.InterfaceResult.CommonPrefixes ? result.InterfaceResult.CommonPrefixes.map(item => ({
        prefix: item.Prefix,
      })) : [];

      // 过滤掉与当前查询前缀相同的文件夹，这通常代表当前目录本身，不应显示为子文件夹
      const filteredFolders = folders.filter(folder => folder.prefix !== prefix);

      // 只在首次列出时显示统计信息
      if (!marker && (files.length > 0 || filteredFolders.length > 0)) {
        console.log(`[listObjects] 找到 ${files.length} 个文件, ${filteredFolders.length} 个文件夹`);
      }
      return {
        files: files,
        folders: filteredFolders,
        nextMarker: result.InterfaceResult.IsTruncated ? result.InterfaceResult.NextMarker : null,
        currentPrefix: prefix,
      };
    } else {
      const errorMsg = `列出OBS对象失败: ${result.CommonMsg.Code} - ${result.CommonMsg.Message}`;
      console.error(errorMsg, result);
      throw new Error(errorMsg);
    }
  } catch (error) {
    console.error('调用OBS SDK listObjects 发生异常:', error);
    // 将原始错误信息包装或直接抛出，以便上层能获取详细错误
    throw new Error(`调用OBS SDK listObjects 发生异常: ${error.message || error}`);
  }
}

/**
 * 规范化目录路径，确保以 '/' 结尾。
 * @param {string} dirPath - 目录路径。
 * @returns {string} 规范化后的目录路径。
 */
function normalizeDirectoryPath(dirPath) {
  if (dirPath && dirPath.length > 0 && !dirPath.endsWith('/')) {
    return dirPath + '/';
  }
  return dirPath || ''; // 处理 null, undefined 或空字符串，返回 ''.
}

/**
  * 递归地将一个"目录"下的所有对象（文件和文件夹占位符）复制到另一个"目录"。
  * @async
  * @param {string} sourceDirectory - 源目录的路径 (例如 'source/folder/' 或 'source/folder')。
  * @param {string} destinationDirectory - 目标目录的路径 (例如 'destination/folder/' 或 'destination/folder')。
  * @param {Set<string>} [existingTargetKeys=new Set()] - 可选参数，包含目标目录中已存在的对象键的Set，用于增量复制。
  * @returns {Promise<CopyStatus[]>} 一个包含每个对象复制状态的数组。
  * @throws {Error} 如果OBS客户端初始化失败或Bucket名称未配置，则抛出错误。
  */
export async function batchCopyObjectsRecursive(sourceDirectory, destinationDirectory, existingTargetKeys = new Set(), signal = null) {
  const obsClient = getObsClient();
  const bucketName = OBS_CONFIG.bucket;

  if (!bucketName) {
    const errorMsg = 'OBS Bucket 名称未在 obsEnv.js 中配置。请检查 OBS_CONFIG.bucket。';
    console.error(errorMsg);
    throw new Error(errorMsg);
  }

  const normalizedSourceDir = normalizeDirectoryPath(sourceDirectory);
  const normalizedDestinationDir = normalizeDirectoryPath(destinationDirectory);

  if (normalizedSourceDir === normalizedDestinationDir) {
    const errorMsg = '源目录和目标目录不能相同。'
    console.error(errorMsg);
    return [{
      sourceKey: normalizedSourceDir,
      destinationKey: normalizedDestinationDir,
      status: 'failure',
      error: errorMsg
    }];
  }

  // 新增：防止将目录复制到其子目录中
  if (normalizedDestinationDir.startsWith(normalizedSourceDir)) {
    const errorMsg = '目标目录不能是源目录的子目录。';
    console.error(errorMsg);
    return [{
      sourceKey: sourceDirectory,
      destinationKey: destinationDirectory,
      status: 'failure',
      error: errorMsg
    }];
  }

  const copyResults = [];
  // 用于在单次 batchCopyObjectsRecursive 操作中跟踪已尝试/成功复制的目标键，防止重复处理
  const processedDestinationKeys = new Set();
  // 新增：用于跟踪已处理的源对象键，以处理分页可能返回重复项的问题
  const processedSourceKeys = new Set();
  let marker = null;
  let isTruncated = false;

  // 并发控制：限制同时进行的复制任务数量，避免过多并发请求导致浏览器卡死
  const MAX_CONCURRENT_COPIES = 5; // 限制最大并发数为5

  // 分批处理函数：将大数组分成小批次处理
  const processBatch = async (tasks, batchSize = MAX_CONCURRENT_COPIES) => {
    const results = [];
    for (let i = 0; i < tasks.length; i += batchSize) {
      // 检查是否被取消
      if (signal && signal.aborted) {
        throw new Error('操作被取消');
      }

      const batch = tasks.slice(i, i + batchSize);
      const batchResults = await Promise.allSettled(batch);
      results.push(...batchResults);

      // 在批次之间添加短暂延迟，避免请求过于密集
      if (i + batchSize < tasks.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    return results;
  };

  // console.log(`开始批量复制，从 '${normalizedSourceDir}' 到 '${normalizedDestinationDir}'`);

  try {
    do {
      const listParams = {
        Bucket: bucketName,
        Prefix: normalizedSourceDir,
        Marker: marker,
        MaxKeys: 500, // 减少单次查询数量，避免一次性获取过多对象
      };

      // 检查是否被取消
      if (signal && signal.aborted) {
        throw new Error('操作被取消');
      }

      const listResult = await obsClient.listObjects(listParams);

      if (listResult.CommonMsg.Status < 300 && listResult.InterfaceResult.Contents) {
        const objectsToCopy = listResult.InterfaceResult.Contents;
        isTruncated = listResult.InterfaceResult.IsTruncated;
        marker = listResult.InterfaceResult.NextMarker || null;

        // 安全检查：避免因OBS响应异常（IsTruncated为true但NextMarker为空）导致的无限循环
        if (isTruncated && !marker) {
          console.warn(`[batchCopyObjectsRecursive] 检测到分页异常：isTruncated为true但NextMarker为空。为避免无限循环，将强制停止分页。前缀: '${normalizedSourceDir}'`);
          isTruncated = false;
        }

        // 构建复制任务，但不立即执行
        const copyTasks = [];

        for (const objectToCopy of objectsToCopy) {
          const sourceKey = objectToCopy.Key;

          // 新增：如果此源对象已处理过（由于分页错误），则跳过
          if (processedSourceKeys.has(sourceKey)) {
            continue;
          }
          processedSourceKeys.add(sourceKey);

          const relativeKey = sourceKey.substring(normalizedSourceDir.length);
          const destinationKey = normalizedDestinationDir + relativeKey;

          if (sourceKey === destinationKey) {
            console.warn(`跳过复制 (源即是目标): ${sourceKey}`);
            copyResults.push({
              sourceKey,
              destinationKey,
              status: 'failure',
              error: '源对象键和目标对象键相同。'
            });
            continue;
          }

          // 检查目标键是否已在目标目录中预先存在 (通过参数传入)
          if (existingTargetKeys.has(destinationKey)) {
            // console.log(`跳过复制 (目标已存在): ${destinationKey} (源: ${sourceKey})`);
            copyResults.push({
              sourceKey,
              destinationKey,
              status: 'skipped_existing', // 新状态，表示因目标已存在而跳过
              error: '目标对象已存在于目标目录中。'
            });
            continue;
          }

          // 检查是否在本次操作中已经处理过这个目标键
          if (processedDestinationKeys.has(destinationKey)) {
            console.warn(`跳过复制 (本次操作中已处理): ${destinationKey}`);
            copyResults.push({
              sourceKey,
              destinationKey,
              status: 'skipped',
              error: '在本次批量操作中已处理过此目标。'
            });
            continue;
          }

          // 标记为已处理，防止重复
          processedDestinationKeys.add(destinationKey);

          // 添加到任务队列
          copyTasks.push(copySingleObject(sourceKey, destinationKey));
        }

        // 检查是否被取消
        if (signal && signal.aborted) {
          throw new Error('操作被取消');
        }

        // 分批执行复制任务，控制并发数量
        if (copyTasks.length > 0) {
          const results = await processBatch(copyTasks, MAX_CONCURRENT_COPIES);

          results.forEach(result => {
            if (result.status === 'fulfilled') {
              copyResults.push(result.value); // result.value 是 copySingleObject 返回的 CopyStatus 对象
            } else {
              // Promise.allSettled 中 rejected 的情况，其 reason 是抛出的错误
              // 我们需要从 result.reason 中提取或构建 CopyStatus
              // 假设 copySingleObject 失败时会抛出带有 sourceKey 和 destinationKey信息的错误，或者返回特定结构
              // 由于 copySingleObject 内部捕获错误并返回特定结构，理论上不应到这里的 rejected 状态，
              // 除非 copySingleObject 本身在 retry 全部失败后抛出了未被捕获的异常，或者参数错误导致直接失败。
              // 为了稳健，我们处理一下。
              console.error(`批量复制中一个任务严重失败:`, result.reason);
              // 尝试从错误中获取关键信息，如果 copySingleObject 抛出的错误包含这些信息
              // 这是一个回退逻辑，理想情况下 copySingleObject 总是返回一个对象
              const sourceKey = result.reason?.sourceKey || '未知源';
              const destinationKey = result.reason?.destinationKey || '未知目标';
              copyResults.push({
                sourceKey: sourceKey,
                destinationKey: destinationKey,
                status: 'failure',
                error: result.reason?.message || '并发任务执行时未知严重错误'
              });
            }
          });
        }

      } else if (listResult.CommonMsg.Status >= 300) {
        const errorMsg = `列出源目录 '${normalizedSourceDir}' 中的对象失败: ${listResult.CommonMsg.Code} - ${listResult.CommonMsg.Message}`;
        console.error(errorMsg, listResult);
        // 如果列出对象失败，则无法继续，抛出错误或返回包含此错误的单一结果
        throw new Error(errorMsg);
      } else {
        // 没有内容，且未出错，可能是空目录或列表结束
        isTruncated = false;
      }
    } while (isTruncated);

    // console.log('批量复制操作完成。');
    return copyResults;

  } catch (error) {
    console.error('批量复制过程中发生严重错误:', error);
    // 对于未在循环中捕获的更高级别的错误（例如，初始 listObjects 失败），也应将其记录
    // 可以选择将此错误作为整体操作失败的指示返回，或者将其添加到 copyResults 中
    // 或者重新抛出，让调用者处理
    throw error; // 重新抛出，让调用者知道整个操作可能未按预期完成
  }
}

/**
 * 复制单个OBS对象。
 * @async
 * @param {string} sourceKey - 源对象的完整键 (例如 'folder/file.txt')。
 * @param {string} destinationKey - 目标对象的完整键 (例如 'newfolder/newfile.txt')。
 * @returns {Promise<CopyStatus>} 包含复制状态的对象。
 * @throws {Error} 如果OBS客户端初始化失败或Bucket名称未配置，则抛出错误。
 */
export async function copySingleObject(sourceKey, destinationKey) {
  const obsClient = getObsClient();
  const bucketName = OBS_CONFIG.bucket;

  if (!bucketName) {
    const errorMsg = 'OBS Bucket 名称未在 obsEnv.js 中配置。请检查 OBS_CONFIG.bucket。';
    console.error(errorMsg);
    throw new Error(errorMsg);
  }

  if (!sourceKey || !destinationKey) {
    const errorMsg = '源对象键和目标对象键不能为空。';
    console.error(errorMsg);
    return { sourceKey, destinationKey, status: 'failure', error: errorMsg };
  }

  if (sourceKey === destinationKey) {
    const errorMsg = '源对象键和目标对象键不能相同。';
    console.warn(errorMsg); // 使用 warn 因为这可能不是一个严重错误，但操作无效
    return { sourceKey, destinationKey, status: 'failure', error: errorMsg };
  }

  const copySource = `${bucketName}/${sourceKey}`;
  // console.log(`准备复制单个对象: '${sourceKey}' 到 '${destinationKey}' (CopySource: '${copySource}')`);

  try {
    const copyObjectParams = {
      Bucket: bucketName,
      Key: destinationKey,
      CopySource: copySource,
      // ACL: 'bucket-owner-full-control', // 可选：根据需要设置ACL
      // MetadataDirective: 'COPY', // 可选：复制元数据
    };

    const copyCall = () => obsClient.copyObject(copyObjectParams);
    const copyResult = await retry(copyCall, 3, 1000, `复制单个对象 '${sourceKey}' 到 '${destinationKey}'`);

    if (copyResult.CommonMsg.Status < 300) {
      // console.log(`成功复制单个对象: '${sourceKey}' 到 '${destinationKey}'`);
      return { sourceKey, destinationKey, status: 'success' };
    } else {
      const errorDetail = `OBS复制错误: ${copyResult.CommonMsg.Code} - ${copyResult.CommonMsg.Message}`;
      console.error(`复制单个对象失败: '${sourceKey}' 到 '${destinationKey}'. 原因: ${errorDetail}`, copyResult);
      return { sourceKey, destinationKey, status: 'failure', error: errorDetail };
    }
  } catch (err) {
    const errorMsg = err.message || (err.CommonMsg ? `${err.CommonMsg.Code} - ${err.CommonMsg.Message}` : '未知复制错误');
    console.error(`复制单个对象 '${sourceKey}' 到 '${destinationKey}' 发生异常:`, err);
    return { sourceKey, destinationKey, status: 'failure', error: `异常: ${errorMsg}` };
  }
}

/**
 * 创建一个0字节的占位符对象来表示一个空文件夹。
 * OBS本身没有文件夹的概念，通常通过创建一个以指定前缀（路径）命名且内容为空的对象来模拟文件夹。
 * @async
 * @param {string} folderKey - 文件夹在OBS上的完整路径，必须以 '/' 结尾 (例如 'myfolder/subfolder/')。
 * @returns {Promise<{key: string, status: 'success' | 'failure', error?: string}>} 操作结果。
 * @throws {Error} 如果OBS客户端初始化失败、Bucket名称未配置或folderKey无效，则抛出错误或返回失败状态。
 */
export async function createFolderPlaceholder(folderKey) {
  const obsClient = getObsClient();
  const bucketName = OBS_CONFIG.bucket;

  if (!bucketName) {
    const errorMsg = 'OBS Bucket 名称未在 obsEnv.js 中配置。请检查 OBS_CONFIG.bucket。';
    console.error(errorMsg);
    // throw new Error(errorMsg); // 或者返回特定错误对象
    return { key: folderKey, status: 'failure', error: errorMsg };
  }

  if (!folderKey || !folderKey.endsWith('/')) {
    const errorMsg = "文件夹路径 (folderKey) 必须提供并且以 '/' 结尾。";
    console.error(errorMsg);
    // throw new Error(errorMsg); // 或者返回特定错误对象
    return { key: folderKey, status: 'failure', error: errorMsg };
  }

  console.log(`准备创建文件夹占位符: '${folderKey}'`);

  try {
    const params = {
      Bucket: bucketName,
      Key: folderKey,
      Body: '', // 空内容
      ContentLength: 0, // 内容长度为0
    };
    const result = await obsClient.putObject(params);

    if (result.CommonMsg.Status < 300) {
      console.log(`成功创建文件夹占位符: '${folderKey}'`);
      return { key: folderKey, status: 'success' };
    } else {
      const errorDetail = `OBS创建文件夹占位符错误: ${result.CommonMsg.Code} - ${result.CommonMsg.Message}`;
      console.error(`创建文件夹占位符 '${folderKey}' 失败. 原因: ${errorDetail}`, result);
      return { key: folderKey, status: 'failure', error: errorDetail };
    }
  } catch (err) {
    const errorMsg = err.message || (err.CommonMsg ? `${err.CommonMsg.Code} - ${err.CommonMsg.Message}` : '未知错误');
    console.error(`创建文件夹占位符 '${folderKey}' 发生异常:`, err);
    return { key: folderKey, status: 'failure', error: `异常: ${errorMsg}` };
  }
}

/**
 * 上传单个文件到OBS。
 * @async
 * @param {string} objectKey - 文件在OBS上的完整路径 (例如 'images/photo.jpg')。
 * @param {File} fileObject - HTML File 对象 (通常来自 `<input type="file">`)。
 * @param {function(number): void} [onProgressCallback] - 可选的回调函数，用于报告上传进度。接收一个0到100之间的数字作为参数。
 * @param {object} [options={}] - 可选的上传参数配置。
 * @param {number} [options.partSize=20 * 1024 * 1024] - 分片大小（字节），默认为20MB。
 * @param {number} [options.taskNum=3] - 并发上传的分片任务数，默认为3。
 * @param {boolean} [options.enableCheckpoint=true] - 是否启用断点续传功能，默认为true。
 * @returns {Promise<{key: string, status: 'success' | 'failure', error?: string, location?: string}>} 操作结果，成功时包含文件的 OBS Location。
 * @throws {Error} 如果OBS客户端初始化失败或Bucket名称未配置，则抛出错误或返回失败状态。
 */
export async function uploadObject(objectKey, fileObject, onProgressCallback, options = {}) {
  const obsClient = getObsClient();
  const bucketName = OBS_CONFIG.bucket;

  if (!bucketName) {
    const errorMsg = 'OBS Bucket 名称未在 obsEnv.js 中配置。请检查 OBS_CONFIG.bucket。';
    console.error(errorMsg);
    return { key: objectKey, status: 'failure', error: errorMsg };
  }

  if (!objectKey) {
    const errorMsg = '目标对象键 (objectKey) 不能为空。';
    console.error(errorMsg);
    return { key: objectKey, status: 'failure', error: errorMsg };
  }

  if (!fileObject || typeof fileObject.name === 'undefined' || typeof fileObject.size === 'undefined') {
    const errorMsg = '提供的 fileObject 无效。它应该是一个HTML File对象。';
    console.error(errorMsg);
    return { key: objectKey, status: 'failure', error: errorMsg };
  }

  const {
    partSize = 20 * 1024 * 1024, // 默认20MB分片
    taskNum = 3,                  // 默认3个并发任务
    enableCheckpoint = true       // 默认启用断点续传
  } = options;

  console.log(`准备上传文件: '${fileObject.name}' 到 '${objectKey}' (大小: ${fileObject.size}字节), 分片大小: ${partSize / 1024 / 1024}MB, 并发任务: ${taskNum}, 断点续传: ${enableCheckpoint}`);

  try {
    const params = {
      Bucket: bucketName,
      Key: objectKey,
      SourceFile: fileObject, // 直接传递File对象给SourceFile
      PartSize: partSize,
      TaskNum: taskNum,
      EnableCheckpoint: enableCheckpoint,
      // ProgressCallback 是 uploadFile 特有的参数，用于进度跟踪
      // 它会在 ObsClient 内部转换为相应的事件监听
      // 我们需要适配 onProgressCallback
    };

    if (typeof onProgressCallback === 'function') {
      // SDK的 uploadFile 进度回调通常是 (transferredAmount, totalAmount, transferSpeed)
      // 我们需要将其转换为百分比
      params.ProgressCallback = (transferredAmount, totalAmount, transferSpeed) => {
        if (totalAmount > 0) {
          const percent = Math.round((transferredAmount * 100) / totalAmount);
          // console.log(`上传进度: ${percent}%, ${transferredAmount}/${totalAmount} bytes, 速度: ${transferSpeed} B/s`);
          onProgressCallback(percent);
        } else {
          // console.log(`上传进度: (总大小未知), ${transferredAmount} bytes, 速度: ${transferSpeed} B/s`);
          onProgressCallback(0); // 或者传递一个特殊值表示进度未知但正在进行
        }
      };
    } else {
      // 如果没有提供回调，SDK默认会打印一些进度到控制台，我们可以选择禁用或保持默认
      // params.EnableLog = false; // 如果不想让SDK打印任何进度信息
    }

    // 使用 uploadFile 方法进行分段上传，它更适合大文件并支持进度
    const result = await obsClient.uploadFile(params);

    if (result.CommonMsg.Status < 300) {
      console.log(`成功上传文件: '${objectKey}'，Location: ${result.InterfaceResult.Location}`);
      return {
        key: objectKey,
        status: 'success',
        location: result.InterfaceResult.Location, // 返回文件的访问URL
      };
    } else {
      const errorDetail = `OBS上传文件错误: ${result.CommonMsg.Code} - ${result.CommonMsg.Message}`;
      console.error(`上传文件 '${objectKey}' 失败. 原因: ${errorDetail}`, result);
      return { key: objectKey, status: 'failure', error: errorDetail };
    }
  } catch (err) {
    const errorMsg = err.message || (err.CommonMsg ? `${err.CommonMsg.Code} - ${err.CommonMsg.Message}` : '未知上传错误');
    console.error(`上传文件 '${objectKey}' 发生异常:`, err);
    return { key: objectKey, status: 'failure', error: `异常: ${errorMsg}` };
  }
}

/**
 * 获取OBS对象的预签名下载URL。
 * 此函数会首先检查内部缓存中是否有有效的预签名URL，以减少不必要的API调用。
 * @async
 * @param {string} objectKey - 要获取下载URL的对象的完整键 (例如 'data/report.pdf')。
 * @param {number} [expiresInSeconds=3600] - 预签名URL的有效时间（秒），默认为1小时 (3600秒)。最大值通常由OBS服务限制。
 * @param {boolean} [forceGenerate=false] - 是否强制重新生成URL，忽略缓存。默认为false。
 * @returns {Promise<{key: string, url: string | null, status: 'success' | 'failure', error?: string}>} 包含预签名URL或错误信息的对象。
 * @throws {Error} 如果OBS客户端初始化失败或Bucket名称未配置，则抛出错误或返回失败状态。
 */
export async function getPresignedUrlForDownload(objectKey, expiresInSeconds = 3600, forceGenerate = false) {
  const obsClient = getObsClient();
  const bucketName = OBS_CONFIG.bucket;

  if (!bucketName) {
    const errorMsg = 'OBS Bucket 名称未在 obsEnv.js 中配置。请检查 OBS_CONFIG.bucket。';
    console.error(errorMsg);
    return { key: objectKey, url: null, status: 'failure', error: errorMsg };
  }

  if (!objectKey) {
    const errorMsg = '对象键 (objectKey) 不能为空。';
    console.error(errorMsg);
    return { key: objectKey, url: null, status: 'failure', error: errorMsg };
  }

  // 检查缓存
  if (!forceGenerate && urlCache.has(objectKey)) {
    const cachedEntry = urlCache.get(objectKey);
    if (cachedEntry && cachedEntry.url && cachedEntry.expiresAt > Date.now()) {
      console.log(`从缓存中获取预签名URL: '${objectKey}'`);
      return { key: objectKey, url: cachedEntry.url, status: 'success' };
    } else {
      urlCache.delete(objectKey); // 如果过期或无效，则删除
      console.log(`缓存中的预签名URL已过期或无效，已为 '${objectKey}' 清理`);
    }
  }

  // 验证 expiresInSeconds 是否在合理范围内
  if (expiresInSeconds <= 0 || expiresInSeconds > 604800) { // 例如，最大7天
    console.warn(`预签名URL的有效时间 ${expiresInSeconds} 秒无效或过长，将使用默认值 3600 秒。`);
    expiresInSeconds = 3600;
  }

  console.log(`准备为对象 '${objectKey}' 生成预签名下载URL，有效期: ${expiresInSeconds} 秒`);

  try {
    const params = {
      Bucket: bucketName,
      Key: objectKey,
      Method: 'GET',
      Expires: expiresInSeconds,
      // 可选：添加 'response-content-disposition' 以建议浏览器下载行为
      // QueryParams: {
      //   'response-content-disposition': `attachment; filename="${objectKey.split('/').pop()}"`
      // }
    };

    const createUrlCall = () => obsClient.createSignedUrlSync(params);
    const result = await retry(createUrlCall, 3, 1000, `为 '${objectKey}' 生成预签名URL`);

    if (result && (result.InterfaceResult?.SignedUrl || result.SignedUrl)) {
      let signedUrl = result.InterfaceResult?.SignedUrl || result.SignedUrl;

      // 如果URL指向华为云OBS，则需要替换为代理路径
      if (signedUrl.includes('obs.ap-southeast-1.myhuaweicloud.com')) {
        // 将华为云OBS的URL替换为本地代理路径
        signedUrl = signedUrl.replace('https://obs.ap-southeast-1.myhuaweicloud.com', `${window.location.protocol}//${window.location.host}`);
      }
      // 如果URL已经包含localhost，说明是通过代理生成的，直接使用

      console.log(`成功为 '${objectKey}' 生成预签名URL: ${signedUrl}`);

      const expiresAt = Date.now() + (expiresInSeconds * 1000) - 5000; // 提前5秒标记过期
      urlCache.set(objectKey, { url: signedUrl, expiresAt });

      // 设置定时器在URL过期时从缓存中移除
      setTimeout(() => {
        const currentCacheEntry = urlCache.get(objectKey);
        if (currentCacheEntry && currentCacheEntry.url === signedUrl && currentCacheEntry.expiresAt <= Date.now()) {
          urlCache.delete(objectKey);
          console.log(`预签名URL缓存已为 '${objectKey}' 清理 (自动过期)`);
        }
      }, expiresInSeconds * 1000); // 使用原始有效期作为setTimeout延迟

      return {
        key: objectKey,
        url: signedUrl,
        status: 'success'
      };
    } else {
      const errorDetail = '生成预签名URL时，OBS SDK未返回有效的URL。';
      console.error(errorDetail, result);
      return { key: objectKey, url: null, status: 'failure', error: errorDetail };
    }
  } catch (err) {
    // retry 函数会处理重试，如果到这里说明所有重试都失败了
    const errorMsg = err.message || (err.CommonMsg ? `${err.CommonMsg.Code} - ${err.CommonMsg.Message}` : '生成预签名URL时发生未知错误');
    console.error(`为 '${objectKey}' 生成预签名下载URL发生异常 (已重试):`, err);
    return { key: objectKey, url: null, status: 'failure', error: `异常 (已重试): ${errorMsg}` };
  }
}

/**
 * 删除单个OBS对象（文件）。
 * @async
 * @param {string} objectKey - 要删除的对象的完整键 (例如 'documents/report.docx')。
 * @returns {Promise<{key: string, status: 'success' | 'failure', error?: string}>} 操作结果。
 * @throws {Error} 如果OBS客户端初始化失败或Bucket名称未配置，则抛出错误或返回失败状态。
 */
export async function deleteObject(objectKey) {
  const obsClient = getObsClient();
  const bucketName = OBS_CONFIG.bucket;

  if (!bucketName) {
    const errorMsg = 'OBS Bucket 名称未在 obsEnv.js 中配置。请检查 OBS_CONFIG.bucket。';
    console.error(errorMsg);
    return { key: objectKey, status: 'failure', error: errorMsg };
  }

  if (!objectKey) {
    const errorMsg = '要删除的对象键 (objectKey) 不能为空。';
    console.error(errorMsg);
    return { key: objectKey, status: 'failure', error: errorMsg };
  }

  // 特别注意：如果 objectKey 以 '/' 结尾，这通常表示一个文件夹占位符。
  // 删除它就像删除任何其他对象一样。
  // 如果要删除的是一个"真实"的文件夹（即包含其他对象的前缀），
  // 则需要使用 deleteObjectsByPrefix。

  console.log(`准备删除对象: '${objectKey}'`);

  try {
    const obsDeleteCall = () => {
      const deleteParams = {
        Bucket: bucketName,
        Key: objectKey,
      };
      return obsClient.deleteObject(deleteParams);
    };

    const result = await retry(obsDeleteCall, 3, 1000, `删除对象 '${objectKey}'`);

    if (result.CommonMsg.Status < 300) {
      console.log(`成功删除对象: '${objectKey}'`);
      return { key: objectKey, status: 'success' };
    } else {
      // 根据OBS的行为，尝试删除一个不存在的对象可能不会返回错误状态码，
      // 而是返回一个成功的状态码（例如204 No Content）。
      // 因此，我们主要关注状态码是否表示实际的API调用错误。
      // 如果需要区分"成功删除"和"对象不存在"，可能需要先检查对象是否存在。
      // 但对于删除操作，通常目标是确保对象最终不存在，所以2xx状态码一般都视为成功。
      const errorDetail = `OBS删除对象错误: ${result.CommonMsg.Code} - ${result.CommonMsg.Message}`;
      console.error(`删除对象 '${objectKey}' 失败. 原因: ${errorDetail}`, result);
      // 即使是删除不存在的对象，如果SDK层面报了错（非2xx），这里也应该标记为failure
      return { key: objectKey, status: 'failure', error: errorDetail };
    }
  } catch (err) {
    const errorMsg = err.message || (err.CommonMsg ? `${err.CommonMsg.Code} - ${err.CommonMsg.Message}` : '删除对象时发生未知错误');
    console.error(`删除对象 '${objectKey}' 发生异常:`, err);
    return { key: objectKey, status: 'failure', error: `异常: ${errorMsg}` };
  }
}

/**
 * 批量删除指定前缀（目录）下的所有对象。
 * 这对于删除"文件夹"非常有用。
 * @async
 * @param {string} prefix - 要删除对象的前缀 (例如 'logs/old/' 或 'temp_files/')。必须以 '/' 结尾。
 * @returns {Promise<BatchDeleteResult>} 包含已删除对象、删除错误和整体状态的对象。
 * @throws {Error} 如果OBS客户端初始化失败、Bucket名称未配置或前缀无效，则抛出错误或返回特定结果。
 */
export async function deleteObjectsByPrefix(prefix) {
  // console.log(`[deleteObjectsByPrefix] 开始批量删除，前缀: "${prefix}"`);

  const obsClient = getObsClient();
  const bucketName = OBS_CONFIG.bucket;

  const resultSummary = {
    deleted: [],
    errors: [],
    prefix: prefix,
    totalAttempted: 0,
    overallStatus: 'failure', // 默认为失败，成功后更新
  };

  if (!bucketName) {
    const errorMsg = 'OBS Bucket 名称未在 obsEnv.js 中配置。请检查 OBS_CONFIG.bucket。';
    console.error(`[deleteObjectsByPrefix] ${errorMsg}`);
    resultSummary.message = errorMsg;
    return resultSummary;
  }

  if (!prefix || !prefix.endsWith('/')) {
    const errorMsg = "要删除的前缀 (prefix) 必须提供并且以 '/' 结尾。";
    console.error(`[deleteObjectsByPrefix] ${errorMsg}`);
    resultSummary.message = errorMsg;
    return resultSummary;
  }

  try {
    let marker = null;
    let isTruncated = true;
    const objectsToDelete = [];

    // 列出所有要删除的对象
    while (isTruncated) {
      const listParams = {
        Bucket: bucketName,
        Prefix: prefix,
        Marker: marker,
        MaxKeys: 1000, // 每次最多列出1000个，这是SDK的上限
        // 注意：这里不设置 Delimiter，以便列出所有以该前缀开头的对象，包括子目录中的文件
      };

      const listObjectsCall = () => obsClient.listObjects(listParams);
      const listResult = await retry(listObjectsCall, 3, 1000, `列出前缀 '${prefix}' 下的对象`);

      if (listResult.CommonMsg.Status < 300) {
        if (listResult.InterfaceResult.Contents && listResult.InterfaceResult.Contents.length > 0) {
          listResult.InterfaceResult.Contents.forEach(obj => objectsToDelete.push({ Key: obj.Key }));
        }

        // 更新循环控制变量
        isTruncated = listResult.InterfaceResult.IsTruncated || false;
        marker = listResult.InterfaceResult.NextMarker || null;

        // 安全检查：避免因OBS响应异常（IsTruncated为true但NextMarker为空）导致的无限循环
        if (isTruncated && !marker) {
          console.warn(`[deleteObjectsByPrefix] 检测到分页异常：isTruncated为true但NextMarker为空。为避免无限循环，将强制停止分页。前缀: '${prefix}'`);
          isTruncated = false;
        }
      } else {
        const errorMsg = `列出前缀 '${prefix}' 下的对象失败: ${listResult.CommonMsg.Code} - ${listResult.CommonMsg.Message}`;
        console.error(errorMsg, listResult);
        resultSummary.message = errorMsg;
        throw new Error(errorMsg); // 中断操作
      }
    }

    resultSummary.totalAttempted = objectsToDelete.length;
    if (objectsToDelete.length === 0) {
      console.log(`[deleteObjectsByPrefix] 前缀 '${prefix}' 下没有找到可删除的对象`);
      resultSummary.overallStatus = 'success';
      resultSummary.message = '没有找到可删除的对象。';
      return resultSummary;
    }

    // console.log(`[deleteObjectsByPrefix] 开始删除 ${objectsToDelete.length} 个对象`);

    // 分批执行删除操作 (OBS批量删除一次最多1000个)
    const batchSize = 1000;
    const batches = [];
    for (let i = 0; i < objectsToDelete.length; i += batchSize) {
      batches.push(objectsToDelete.slice(i, i + batchSize));
    }

    // 逐批处理删除
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];
      if (batches.length > 1) {
        console.log(`[deleteObjectsByPrefix] 处理第 ${batchIndex + 1}/${batches.length} 批`);
      }

      try {
        const deleteParams = {
          Bucket: bucketName,
          Quiet: false, // 设置为false以获取每个对象删除成功或失败的详细信息
          Objects: batch,
        };

        const deleteObjectsCall = () => obsClient.deleteObjects(deleteParams);
        const deleteResult = await retry(deleteObjectsCall, 3, 1000, `批量删除前缀 '${prefix}' 第 ${batchIndex + 1} 批对象`);

        if (deleteResult.CommonMsg.Status < 300) {
          // 处理成功删除的对象
          if (deleteResult.InterfaceResult.Deleteds && deleteResult.InterfaceResult.Deleteds.length > 0) {
            deleteResult.InterfaceResult.Deleteds.forEach(deletedObj => {
              if (deletedObj.Key) {
                resultSummary.deleted.push(deletedObj.Key);
              }
            });
          }

          // 处理删除失败的对象
          if (deleteResult.InterfaceResult.Errors && deleteResult.InterfaceResult.Errors.length > 0) {
            deleteResult.InterfaceResult.Errors.forEach(errorObj => {
              resultSummary.errors.push({
                Key: errorObj.Key,
                Code: errorObj.Code,
                Message: errorObj.Message,
              });
            });
          }

          // 只在多批次或有错误时显示详细信息
          if (batches.length > 1 || (deleteResult.InterfaceResult.Errors && deleteResult.InterfaceResult.Errors.length > 0)) {
            console.log(`[deleteObjectsByPrefix] 第 ${batchIndex + 1} 批: 成功 ${deleteResult.InterfaceResult.Deleteds?.length || 0}, 失败 ${deleteResult.InterfaceResult.Errors?.length || 0}`);
          }
        } else {
          const errorMsg = `第 ${batchIndex + 1} 批删除失败: ${deleteResult.CommonMsg.Code} - ${deleteResult.CommonMsg.Message}`;
          console.error(errorMsg, deleteResult);
          // 将这批所有对象都标记为错误
          batch.forEach(obj => {
            resultSummary.errors.push({
              Key: obj.Key,
              Code: deleteResult.CommonMsg.Code || 'BATCH_FAILED',
              Message: errorMsg
            });
          });
        }
      } catch (batchError) {
        const errorMsg = `第 ${batchIndex + 1} 批处理异常: ${batchError.message}`;
        console.error(errorMsg, batchError);
        // 将这批所有对象都标记为错误
        batch.forEach(obj => {
          resultSummary.errors.push({
            Key: obj.Key,
            Code: 'BATCH_EXCEPTION',
            Message: errorMsg
          });
        });
      }

      // 添加小延迟避免过于频繁的请求
      if (batchIndex < batches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    // 确定整体状态
    if (resultSummary.errors.length === 0 && resultSummary.deleted.length === resultSummary.totalAttempted) {
      resultSummary.overallStatus = 'success';
      resultSummary.message = `成功删除前缀 '${prefix}' 下的所有 ${resultSummary.totalAttempted} 个对象。`;
      console.log(`[deleteObjectsByPrefix] ${resultSummary.message}`);
    } else if (resultSummary.errors.length > 0 && resultSummary.deleted.length > 0) {
      resultSummary.overallStatus = 'partial_success';
      resultSummary.message = `部分成功删除前缀 '${prefix}' 下的对象。成功: ${resultSummary.deleted.length}, 失败: ${resultSummary.errors.length}。`;
      console.warn(`[deleteObjectsByPrefix] ${resultSummary.message}`);
    } else if (resultSummary.errors.length > 0 && resultSummary.deleted.length === 0) {
      resultSummary.overallStatus = 'failure';
      resultSummary.message = `未能删除前缀 '${prefix}' 下的任何对象。共尝试 ${resultSummary.totalAttempted} 个，全部失败。`;
      console.error(`[deleteObjectsByPrefix] ${resultSummary.message}`);
    } else {
      resultSummary.overallStatus = 'failure';
      resultSummary.message = `批量删除操作完成，但状态未知。已删除: ${resultSummary.deleted.length}, 错误: ${resultSummary.errors.length} (总尝试: ${resultSummary.totalAttempted})。`;
      console.warn(`[deleteObjectsByPrefix] ${resultSummary.message}`);
    }

    return resultSummary;

  } catch (error) {
    const errorMsg = error.message || '批量删除过程中发生严重异常';
    console.error(`批量删除前缀 '${prefix}' 发生严重错误:`, error);
    resultSummary.message = errorMsg;
    resultSummary.overallStatus = 'failure';
    // 如果在列出对象之后、删除操作之前发生错误，errors列表可能为空。
    // 可以选择将所有预期要删除的对象（如果已列出）放入errors中。
    if (resultSummary.totalAttempted > 0 && resultSummary.errors.length === 0 && resultSummary.deleted.length === 0) {
      // 假设错误发生在列出对象之后，但在实际删除API调用之前或期间
      // 并且 deleteObjects 本身没有成功返回过任何错误列表
    }
    return resultSummary;
  }
}

/**
 * 批量删除指定的对象键列表（优化版本）。
 * @async
 * @param {string[]} keys - 要删除的对象的完整键的数组 (例如 ['file1.txt', 'folder/file2.jpg'])。
 * @param {number} [batchSize=1000] - 每批处理的对象数量，默认1000。
 * @param {function(number, number): void} [onProgress] - 进度回调函数，接收 (已处理数量, 总数量) 参数。
 * @returns {Promise<BatchDeleteResult>} 包含已删除对象、删除错误和整体状态的对象。
 * @throws {Error} 如果OBS客户端初始化失败或Bucket名称未配置，则可能抛出错误或返回特定结果。
 */
export async function batchDeleteFiles(keys, batchSize = 1000, onProgress = null) {
  const obsClient = getObsClient();
  const bucketName = OBS_CONFIG.bucket;

  const resultSummary = {
    deleted: [],
    errors: [],
    prefix: null, // Not applicable for key list deletion
    totalAttempted: keys ? keys.length : 0,
    overallStatus: 'failure',
  };

  if (!bucketName) {
    const errorMsg = 'OBS Bucket 名称未在 obsEnv.js 中配置。请检查 OBS_CONFIG.bucket。';
    console.error(errorMsg);
    resultSummary.message = errorMsg;
    return resultSummary;
  }

  if (!keys || keys.length === 0) {
    console.log('没有提供需要删除的文件键。');
    resultSummary.message = '没有提供需要删除的文件键。';
    resultSummary.overallStatus = 'success';
    return resultSummary;
  }

  // console.log(`[batchDeleteFiles] 开始删除 ${keys.length} 个文件`);

  try {
    // 将键分批处理
    const batches = [];
    for (let i = 0; i < keys.length; i += batchSize) {
      batches.push(keys.slice(i, i + batchSize));
    }

    let processedCount = 0;

    // 逐批处理删除
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];
      const objectsToDelete = batch.map(key => ({ Key: key }));

      try {
        const deleteParams = {
          Bucket: bucketName,
          Quiet: false, // 获取详细结果
          Objects: objectsToDelete,
        };

        const deleteObjectsCall = () => obsClient.deleteObjects(deleteParams);
        const deleteResult = await retry(deleteObjectsCall, 3, 1000, `批量删除第 ${batchIndex + 1} 批文件`);

        if (deleteResult.CommonMsg.Status < 300) {
          // 处理成功删除的对象
          if (deleteResult.InterfaceResult.Deleteds && deleteResult.InterfaceResult.Deleteds.length > 0) {
            deleteResult.InterfaceResult.Deleteds.forEach(deletedObj => {
              resultSummary.deleted.push(deletedObj.Key);
            });
          }

          // 处理删除失败的对象
          if (deleteResult.InterfaceResult.Errors && deleteResult.InterfaceResult.Errors.length > 0) {
            deleteResult.InterfaceResult.Errors.forEach(errorObj => {
              resultSummary.errors.push({
                Key: errorObj.Key,
                Code: errorObj.Code,
                Message: errorObj.Message,
              });
            });
          }

          // 只在有错误时显示详细信息
          if (deleteResult.InterfaceResult.Errors && deleteResult.InterfaceResult.Errors.length > 0) {
            console.log(`[batchDeleteFiles] 第 ${batchIndex + 1} 批: 成功 ${deleteResult.InterfaceResult.Deleteds?.length || 0}, 失败 ${deleteResult.InterfaceResult.Errors?.length || 0}`);
          }
        } else {
          // 整批失败，将所有对象标记为错误
          const errorMsg = `第 ${batchIndex + 1} 批删除失败: ${deleteResult.CommonMsg.Code} - ${deleteResult.CommonMsg.Message}`;
          console.error(`[batchDeleteFiles] ${errorMsg}`);

          batch.forEach(key => {
            resultSummary.errors.push({
              Key: key,
              Code: deleteResult.CommonMsg.Code || 'BATCH_FAILED',
              Message: errorMsg
            });
          });
        }
      } catch (batchError) {
        // 单批异常处理
        const errorMsg = `第 ${batchIndex + 1} 批处理异常: ${batchError.message}`;
        console.error(`[batchDeleteFiles] ${errorMsg}`, batchError);

        batch.forEach(key => {
          resultSummary.errors.push({
            Key: key,
            Code: 'BATCH_EXCEPTION',
            Message: errorMsg
          });
        });
      }

      // 更新进度
      processedCount += batch.length;

      if (typeof onProgress === 'function') {
        try {
          onProgress(processedCount, keys.length);
        } catch (progressError) {
          console.warn(`[batchDeleteFiles] 进度回调执行失败:`, progressError);
        }
      }

      // 添加小延迟避免过于频繁的请求
      if (batchIndex < batches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    // 确定整体状态
    if (resultSummary.errors.length === 0 && resultSummary.deleted.length === resultSummary.totalAttempted) {
      resultSummary.overallStatus = 'success';
      resultSummary.message = `成功删除所有 ${resultSummary.totalAttempted} 个文件。`;
    } else if (resultSummary.errors.length > 0 && resultSummary.deleted.length > 0) {
      resultSummary.overallStatus = 'partial_success';
      resultSummary.message = `部分成功删除文件。成功: ${resultSummary.deleted.length}, 失败: ${resultSummary.errors.length}。`;
    } else if (resultSummary.errors.length > 0 && resultSummary.deleted.length === 0) {
      resultSummary.overallStatus = 'failure';
      resultSummary.message = `未能删除任何文件。共尝试 ${resultSummary.totalAttempted} 个，全部失败。`;
    } else {
      resultSummary.overallStatus = resultSummary.totalAttempted === 0 ? 'success' : 'failure';
      resultSummary.message = resultSummary.overallStatus === 'success' ?
        '没有文件需要删除。' :
        `批量删除操作状态未知。已删除: ${resultSummary.deleted.length}, 错误: ${resultSummary.errors.length}。`;
    }

    // console.log(`[batchDeleteFiles] 批量删除完成: ${resultSummary.message}`);
    return resultSummary;

  } catch (error) {
    const errorMsg = error.message || '批量删除文件过程中发生严重异常';
    console.error(`[batchDeleteFiles] ${errorMsg}`, error);
    resultSummary.message = errorMsg;
    resultSummary.overallStatus = 'failure';

    // 确保所有尝试的文件都被记录为错误（如果它们尚未在 errors 列表中）
    keys.forEach(key => {
      if (!resultSummary.errors.find(e => e.Key === key)) {
        resultSummary.errors.push({
          Key: key,
          Code: error.code || 'UNKNOWN_ERROR',
          Message: error.message || '未知异常'
        });
      }
    });
    return resultSummary;
  }
}


