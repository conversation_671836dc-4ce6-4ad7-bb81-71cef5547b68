// 服务器端数据库服务
// 使用服务器API存储在线人数历史数据
import axios from 'axios'

// 服务器类型枚举（避免循环引用）
export const SERVER_TYPE = {
  PRODUCTION: 'PRODUCTION', // 正式服
  TEST: 'TEST'              // 测试服
}

// 服务器API配置
const API_CONFIG = {
  // 正式服
  [SERVER_TYPE.PRODUCTION]: {
    baseUrl: 'http://111.91.1.198:5174/api',
    tableName: 'player_stats_production'
  },
  // 测试服
  [SERVER_TYPE.TEST]: {
    baseUrl: 'http://135.181.78.188:8080/api',
    tableName: 'player_stats_test'
  }
}

// 检查API是否可用的函数
const checkApiAvailability = async (serverType) => {
  try {
    const config = API_CONFIG[serverType]
    const response = await fetch(`${config.baseUrl}/stats/ping`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
      mode: 'cors',
      timeout: 5000
    })

    if (response.ok) {
      console.log(`${serverType}服务器API可用`)
      return true
    } else {
      console.warn(`${serverType}服务器API返回非200状态码:`, response.status)
      return false
    }
  } catch (error) {
    console.error(`${serverType}服务器API不可用:`, error)
    return false
  }
}

// 创建API实例
const createApiInstance = (serverType) => {
  const config = API_CONFIG[serverType]
  return axios.create({
    baseURL: config.baseUrl,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 处理响应数据
const handleResponse = (response) => {
  if (response.data) {
    if (response.data.code !== undefined) {
      if (response.data.code === 1 || response.data.code === 200 || response.data.code === 0) {
        return response.data.data || response.data
      }
      throw new Error(response.data.msg || '请求失败')
    }
    return response.data
  }
  throw new Error('请求失败，无效的响应数据')
}

// 保存玩家统计数据
const savePlayerStats = async (count, serverType = SERVER_TYPE.PRODUCTION) => {
  try {
    const api = createApiInstance(serverType)
    const config = API_CONFIG[serverType]

    // 获取当前时间
    const now = new Date()

    // 将时间对齐到最近的15分钟
    // 例如：10:07 会被对齐到 10:00，10:23 会被对齐到 10:15
    const minutes = now.getMinutes()
    const alignedMinutes = Math.floor(minutes / 15) * 15
    now.setMinutes(alignedMinutes)
    now.setSeconds(0)
    now.setMilliseconds(0)

    // 创建记录，确保count是整数，并且时间戳按15分钟对齐
    const record = {
      timestamp: now.getTime(),
      count: Math.round(count), // 确保存储的是整数
      date: now.toISOString(),
      server_type: serverType,
      table_name: config.tableName
    }

    console.log(`保存玩家统计数据到${serverType}服务器:`, record)

    // 发送到服务器API
    const response = await api.post('/stats/save', record)
    const result = handleResponse(response)

    console.log(`玩家统计数据保存成功:`, result)
    return result
  } catch (error) {
    console.error(`保存玩家统计数据到${serverType}服务器失败:`, error)
    // 如果服务器API失败，尝试使用本地存储作为备份
    return saveToLocalStorage(count, serverType)
  }
}

// 本地存储备份（当服务器API不可用时使用）
const saveToLocalStorage = (count, serverType) => {
  try {
    const now = new Date()
    const minutes = now.getMinutes()
    const alignedMinutes = Math.floor(minutes / 15) * 15
    now.setMinutes(alignedMinutes)
    now.setSeconds(0)
    now.setMilliseconds(0)

    const record = {
      timestamp: now.getTime(),
      count: Math.round(count),
      date: now.toISOString(),
      server_type: serverType
    }

    // 获取现有数据
    const key = `player_stats_${serverType.toLowerCase()}`
    let existingData = localStorage.getItem(key)
    let dataArray = existingData ? JSON.parse(existingData) : []

    // 检查是否已存在相同时间戳的记录
    const existingIndex = dataArray.findIndex(item => item.timestamp === record.timestamp)
    if (existingIndex >= 0) {
      // 更新现有记录
      dataArray[existingIndex] = record
    } else {
      // 添加新记录
      dataArray.push(record)
    }

    // 保存回本地存储
    localStorage.setItem(key, JSON.stringify(dataArray))
    console.log(`玩家统计数据已保存到本地存储(${serverType}):`, record)
    return record
  } catch (error) {
    console.error(`保存玩家统计数据到本地存储失败:`, error)
    throw error
  }
}

// 获取指定时间范围内的玩家统计数据
const getPlayerStats = async (startTime, endTime, serverType = SERVER_TYPE.PRODUCTION) => {
  try {
    // 首先检查API是否可用
    const apiAvailable = await checkApiAvailability(serverType)
    if (!apiAvailable) {
      console.warn(`${serverType}服务器API不可用，使用本地存储数据`)
      return getFromLocalStorage(startTime, endTime, serverType)
    }

    const api = createApiInstance(serverType)
    const config = API_CONFIG[serverType]

    console.log(`从${serverType}服务器获取玩家统计数据，时间范围:`, new Date(startTime), '至', new Date(endTime))

    // 发送到服务器API
    console.log(`请求统计数据API: ${config.baseUrl}/stats/query?startTime=${startTime}&endTime=${endTime}&tableName=${config.tableName}`)

    try {
      const response = await api.get('/stats/query', {
        params: {
          startTime,
          endTime,
          tableName: config.tableName
        },
        timeout: 10000 // 增加超时时间
      })

      const results = handleResponse(response)

      // 确保结果是数组并按时间戳排序
      if (Array.isArray(results)) {
        results.sort((a, b) => a.timestamp - b.timestamp)
        console.log(`从${serverType}服务器获取了 ${results.length} 条历史数据`)
        return results
      }

      console.warn(`从${serverType}服务器获取的数据格式不正确:`, results)
    } catch (apiError) {
      console.error(`API请求失败:`, apiError.message || apiError)
    }

    // 如果API请求失败或返回格式不正确，尝试从本地存储获取
    console.log(`尝试从本地存储获取${serverType}服务器数据`)
    return getFromLocalStorage(startTime, endTime, serverType)
  } catch (error) {
    console.error(`从${serverType}服务器获取玩家统计数据失败:`, error)
    // 如果服务器API失败，尝试从本地存储获取
    return getFromLocalStorage(startTime, endTime, serverType)
  }
}

// 从本地存储获取数据（当服务器API不可用时使用）
const getFromLocalStorage = (startTime, endTime, serverType) => {
  try {
    const key = `player_stats_${serverType.toLowerCase()}`
    const storedData = localStorage.getItem(key)

    if (!storedData) {
      console.warn(`本地存储中没有${serverType}服务器的数据`)
      return []
    }

    const allData = JSON.parse(storedData)

    // 过滤指定时间范围内的数据
    const filteredData = allData.filter(item =>
      item.timestamp >= startTime && item.timestamp <= endTime
    )

    // 按时间戳排序
    filteredData.sort((a, b) => a.timestamp - b.timestamp)

    console.log(`从本地存储获取了 ${filteredData.length} 条${serverType}服务器的历史数据`)
    return filteredData
  } catch (error) {
    console.error(`从本地存储获取${serverType}服务器数据失败:`, error)
    return []
  }
}

// 清理过期数据（保留最近90天的数据）
const cleanupOldData = async (serverType = SERVER_TYPE.PRODUCTION) => {
  try {
    const api = createApiInstance(serverType)
    const config = API_CONFIG[serverType]
    const ninetyDaysAgo = Date.now() - (90 * 24 * 60 * 60 * 1000)

    console.log(`清理${serverType}服务器90天前的数据`)

    // 发送到服务器API
    const response = await api.delete('/stats/cleanup', {
      params: {
        beforeTime: ninetyDaysAgo,
        tableName: config.tableName
      }
    })

    const result = handleResponse(response)
    console.log(`清理${serverType}服务器过期数据完成:`, result)

    // 同时清理本地存储中的过期数据
    cleanupLocalStorage(serverType, ninetyDaysAgo)

    return result.deletedCount || 0
  } catch (error) {
    console.error(`清理${serverType}服务器过期数据失败:`, error)
    // 如果服务器API失败，只清理本地存储
    return cleanupLocalStorage(serverType, Date.now() - (90 * 24 * 60 * 60 * 1000))
  }
}

// 清理本地存储中的过期数据
const cleanupLocalStorage = (serverType, beforeTime) => {
  try {
    const key = `player_stats_${serverType.toLowerCase()}`
    const storedData = localStorage.getItem(key)

    if (!storedData) {
      return 0
    }

    const allData = JSON.parse(storedData)
    const originalLength = allData.length

    // 过滤掉过期数据
    const newData = allData.filter(item => item.timestamp >= beforeTime)

    // 保存回本地存储
    localStorage.setItem(key, JSON.stringify(newData))

    const deletedCount = originalLength - newData.length
    console.log(`从本地存储中清理了 ${deletedCount} 条${serverType}服务器的过期数据`)
    return deletedCount
  } catch (error) {
    console.error(`清理本地存储中的${serverType}服务器过期数据失败:`, error)
    return 0
  }
}

// 获取最新的玩家统计数据
const getLatestPlayerStats = async (serverType = SERVER_TYPE.PRODUCTION) => {
  try {
    const api = createApiInstance(serverType)
    const config = API_CONFIG[serverType]

    console.log(`获取${serverType}服务器最新的玩家统计数据`)

    // 发送到服务器API
    const response = await api.get('/stats/latest', {
      params: {
        tableName: config.tableName
      }
    })

    const result = handleResponse(response)
    console.log(`获取${serverType}服务器最新数据成功:`, result)
    return result
  } catch (error) {
    console.error(`获取${serverType}服务器最新数据失败:`, error)
    // 如果服务器API失败，尝试从本地存储获取
    return getLatestFromLocalStorage(serverType)
  }
}

// 从本地存储获取最新数据
const getLatestFromLocalStorage = (serverType) => {
  try {
    const key = `player_stats_${serverType.toLowerCase()}`
    const storedData = localStorage.getItem(key)

    if (!storedData) {
      console.warn(`本地存储中没有${serverType}服务器的数据`)
      return null
    }

    const allData = JSON.parse(storedData)

    if (allData.length === 0) {
      return null
    }

    // 按时间戳排序并获取最新的
    allData.sort((a, b) => b.timestamp - a.timestamp)

    console.log(`从本地存储获取了${serverType}服务器的最新数据:`, allData[0])
    return allData[0]
  } catch (error) {
    console.error(`从本地存储获取${serverType}服务器最新数据失败:`, error)
    return null
  }
}

// 计算指定时间范围内的数据变化百分比
const calculateChangePercentage = (data) => {
  if (!data || data.length < 2) return 0

  const firstValue = data[0].count
  const lastValue = data[data.length - 1].count

  // 返回数字类型而不是字符串
  return Number(((lastValue - firstValue) / firstValue * 100).toFixed(2))
}

// 导出数据库服务
export const dbService = {
  savePlayerStats,
  getPlayerStats,
  cleanupOldData,
  getLatestPlayerStats,
  calculateChangePercentage,
  checkApiAvailability
}
