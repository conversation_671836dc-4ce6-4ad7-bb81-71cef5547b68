// 定时任务服务
// 用于定期执行数据收集任务

import { gameService } from './gameService'
import { dbService, SERVER_TYPE } from './dbService'

// 定时器ID
let dataCollectionTimerId = null

// 默认间隔时间（15分钟）
const DEFAULT_INTERVAL = 15 * 60 * 1000

// 数据清理定时器ID
let dataCleanupTimerId = null

// 上次数据收集的时间戳
let lastCollectionTime = 0

// 启动数据收集任务
const startDataCollection = (interval = DEFAULT_INTERVAL) => {
  // 如果已经有定时器在运行，先停止它
  if (dataCollectionTimerId) {
    stopDataCollection()
  }

  // 设置定时器，定期执行数据收集
  dataCollectionTimerId = setInterval(async () => {
    // 获取当前时间
    const now = new Date()

    // 将时间对齐到最近的15分钟
    const minutes = now.getMinutes()
    const alignedMinutes = Math.floor(minutes / 15) * 15
    now.setMinutes(alignedMinutes)
    now.setSeconds(0)
    now.setMilliseconds(0)

    // 计算对齐后的时间戳
    const alignedTimestamp = now.getTime()

    // 如果与上次收集的时间戳相同，则跳过
    if (alignedTimestamp === lastCollectionTime) {
      console.log('跳过数据收集，因为时间戳与上次相同:', new Date(alignedTimestamp).toLocaleString())
      return
    }

    // 更新上次收集时间
    lastCollectionTime = alignedTimestamp

    // 执行数据收集
    try {
      await collectData()
    } catch (error) {
      console.error('定时数据收集失败:', error)
    }
  }, interval)

  console.log(`数据收集任务已启动，间隔时间: ${interval / 1000} 秒`)

  // 每天执行一次数据清理
  const oneDayInMs = 24 * 60 * 60 * 1000

  // 如果已经有清理定时器在运行，先停止它
  if (dataCleanupTimerId) {
    clearInterval(dataCleanupTimerId)
  }

  // 设置清理定时器
  dataCleanupTimerId = setInterval(() => {
    // 获取当前服务器类型
    const currentServerType = gameService.getCurrentServerType()

    // 清理当前服务器类型的数据
    dbService.cleanupOldData(currentServerType)
      .then(count => console.log(`${currentServerType}服务器数据清理完成，删除了 ${count} 条过期数据`))
      .catch(error => console.error(`${currentServerType}服务器数据清理失败:`, error))
  }, oneDayInMs)

  return true
}

// 停止数据收集任务
const stopDataCollection = () => {
  let stopped = false

  // 停止数据收集定时器
  if (dataCollectionTimerId) {
    clearInterval(dataCollectionTimerId)
    dataCollectionTimerId = null
    stopped = true
    console.log('数据收集任务已停止')
  }

  // 停止数据清理定时器
  if (dataCleanupTimerId) {
    clearInterval(dataCleanupTimerId)
    dataCleanupTimerId = null
    stopped = true
    console.log('数据清理任务已停止')
  }

  return stopped
}

// 收集数据
const collectData = async () => {
  try {
    console.log('开始收集在线玩家数据...')

    // 获取当前服务器类型
    const currentServerType = gameService.getCurrentServerType()
    console.log(`当前服务器类型: ${currentServerType}`)

    // 获取网关用户数据
    const gatewayClients = await gameService.getGatewayClients()

    // 确定要保存的玩家数量
    let count = 0

    if (playersCount && typeof playersCount.count === 'number') {
      count = playersCount.count
    } else if (typeof playersCount === 'number') {
      count = playersCount
    } else if (Array.isArray(gatewayClients)) {
      count = gatewayClients.length
    }

    // 保存到数据库，传递当前服务器类型
    await dbService.savePlayerStats(count, currentServerType)

    console.log(`数据收集完成，当前在线玩家数: ${count}，服务器类型: ${currentServerType}`)
    return count
  } catch (error) {
    console.error('数据收集失败:', error)
    throw error
  }
}

// 手动触发数据收集
const triggerDataCollection = async () => {
  try {
    // 获取当前时间
    const now = new Date()

    // 将时间对齐到最近的15分钟
    const minutes = now.getMinutes()
    const alignedMinutes = Math.floor(minutes / 15) * 15
    now.setMinutes(alignedMinutes)
    now.setSeconds(0)
    now.setMilliseconds(0)

    // 计算对齐后的时间戳
    const alignedTimestamp = now.getTime()

    // 获取当前服务器类型
    const currentServerType = gameService.getCurrentServerType()

    // 如果与上次收集的时间戳相同，则跳过
    if (alignedTimestamp === lastCollectionTime) {
      console.log(`跳过${currentServerType}服务器手动数据收集，因为时间戳与上次相同:`, new Date(alignedTimestamp).toLocaleString())
      // 返回上次收集的结果
      return null
    }

    // 更新上次收集时间
    lastCollectionTime = alignedTimestamp

    // 执行数据收集
    return await collectData()
  } catch (error) {
    console.error('手动触发数据收集失败:', error)
    throw error
  }
}

// 导出调度器服务
export const schedulerService = {
  startDataCollection,
  stopDataCollection,
  triggerDataCollection
}
