// 本地存储服务

// 存储键名
const STORAGE_KEYS = {
  BTC_ADDRESS_NOTES: 'btc_address_notes'
}

// 获取BTC地址备注
const getBtcAddressNotes = () => {
  try {
    const notesJson = localStorage.getItem(STORAGE_KEYS.BTC_ADDRESS_NOTES)
    return notesJson ? JSON.parse(notesJson) : {}
  } catch (error) {
    console.error('获取BTC地址备注失败:', error)
    return {}
  }
}

// 保存BTC地址备注
const saveBtcAddressNote = (address, note) => {
  try {
    const notes = getBtcAddressNotes()
    notes[address] = note
    localStorage.setItem(STORAGE_KEYS.BTC_ADDRESS_NOTES, JSON.stringify(notes))
    return true
  } catch (error) {
    console.error('保存BTC地址备注失败:', error)
    return false
  }
}

// 删除BTC地址备注
const deleteBtcAddressNote = (address) => {
  try {
    const notes = getBtcAddressNotes()
    if (notes[address]) {
      delete notes[address]
      localStorage.setItem(STORAGE_KEYS.BTC_ADDRESS_NOTES, JSON.stringify(notes))
    }
    return true
  } catch (error) {
    console.error('删除BTC地址备注失败:', error)
    return false
  }
}

// 获取单个BTC地址的备注
const getBtcAddressNote = (address) => {
  const notes = getBtcAddressNotes()
  return notes[address] || ''
}

export const storageService = {
  getBtcAddressNotes,
  saveBtcAddressNote,
  deleteBtcAddressNote,
  getBtcAddressNote
}
