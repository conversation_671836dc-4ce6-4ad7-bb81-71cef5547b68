import axios from 'axios'
import { mockGameService } from '../mock/mockGameService'
import { dbService, SERVER_TYPE } from './dbService'

// 判断是否使用模拟数据（开发环境）
const useMockData = false

// 是否使用数据库存储历史数据
const useDB = true

// 导出服务器类型枚举（从dbService导入）
export { SERVER_TYPE }

// 服务器配置
const SERVER_CONFIG = {
  // 正式服
  [SERVER_TYPE.PRODUCTION]: {
    url: '/api', // 使用Vite代理
    // apiKey: '549617ef-80dd-4589-9676-fec7d295e89c',
    name: '正式服',
    baseUrl: 'http://111.91.1.198:5174'
  },
  // 测试服
  [SERVER_TYPE.TEST]: {
    url: '/api', // 使用Vite代理
    // apiKey: '549617ef-80dd-4589-9676-fec7d295e89c',
    name: '测试服',
    baseUrl: 'http://135.181.78.188:8080'
  }
}

// 当前使用的服务器类型
let currentServerType = SERVER_TYPE.PRODUCTION

// 当前使用的服务器配置
let CURRENT_SERVER = SERVER_CONFIG[currentServerType]

// 创建 axios 实例
const api = axios.create({
  // 使用服务器的完整URL + api路径
  baseURL: CURRENT_SERVER.baseUrl + '/api',
  timeout: 30000, // 增加默认超时时间到30秒
  headers: {
    'admin_api_key': CURRENT_SERVER.apiKey
  }
})

// 处理响应数据的工具函数
const handleResponse = (response) => {
  // 直接返回数据，根据实际API响应格式调整
  if (response.data) {
    // 如果有code字段，则按照原来的逻辑处理
    if (response.data.code !== undefined) {
      if (response.data.code === 1 || response.data.code === 200 || response.data.code === 0) {
        return response.data.data || response.data
      }
      throw new Error(response.data.msg || '请求失败')
    }
    // 如果没有code字段，直接返回数据
    return response.data
  }
  throw new Error('请求失败，无效的响应数据')
}

// 切换服务器类型
const switchServerType = (serverType) => {
  if (!SERVER_CONFIG[serverType]) {
    console.error(`无效的服务器类型: ${serverType}`)
    return false
  }

  // 更新当前服务器类型和配置
  currentServerType = serverType
  CURRENT_SERVER = SERVER_CONFIG[serverType]

  // 更新 API 实例的基础 URL
  api.defaults.baseURL = CURRENT_SERVER.baseUrl + '/api'

  console.log(`已切换到${CURRENT_SERVER.name}，基础URL: ${CURRENT_SERVER.baseUrl}/api`)
  return true
}

export const gameService = {
  // 获取当前服务器类型
  getCurrentServerType() {
    return currentServerType
  },

  // 获取当前服务器配置
  getCurrentServer() {
    return CURRENT_SERVER
  },

  // 获取所有服务器配置
  getAllServerConfigs() {
    return SERVER_CONFIG
  },

  // 切换服务器类型
  switchServerType,
  // 获取所有房间信息
  async getAllRooms() {
    if (useMockData) {
      return mockGameService.getAllRooms()
    }

    try {
      console.log('请求房间列表接口:', api.defaults.baseURL + '/rooms')

      // 设置更长的超时时间
      const response = await api.get('/rooms', { timeout: 30000 })
      console.log('房间列表响应:', response.data)

      // 处理特定的响应格式，提取rooms数组
      const responseData = handleResponse(response)
      if (responseData && responseData.rooms && Array.isArray(responseData.rooms)) {
        return responseData.rooms
      } else if (Array.isArray(responseData)) {
        return responseData
      } else {
        console.warn('房间列表数据格式不符合预期:', responseData)
        return []
      }
    } catch (error) {
      // 提供更详细的错误信息
      if (error.code === 'ECONNABORTED') {
        console.error('获取房间列表超时，请检查网络连接或服务器状态')
      } else if (error.code === 'ETIMEDOUT') {
        console.error('连接服务器超时，请检查服务器地址是否正确或服务器是否在线')
      } else {
        console.error('获取房间列表失败:',
          error.response ? error.response.data :
          (error.message || '未知错误'),
          '错误代码:', error.code || '无')
      }

      // 如果服务器没有响应，返回空数组
      return []
    }
  },

  // 获取特定房间的信息
  async getRoomById(roomId) {
    if (useMockData) {
      return mockGameService.getRoomById(roomId)
    }

    try {
      console.log(`请求房间详情接口:`, api.defaults.baseURL + `/rooms/${roomId}`)
      const response = await api.get(`/rooms/${roomId}`)
      console.log(`房间详情响应:`, response.data)
      return handleResponse(response)
    } catch (error) {
      console.error(`获取房间 ${roomId} 信息失败:`, error.response ? error.response.data : error.message)
      // 如果服务器没有响应，返回空对象
      return {}
    }
  },

  // 获取特定游戏节点的玩家信息
  async getGamePlayers(gameId) {
    if (useMockData) {
      return mockGameService.getGamePlayers(gameId)
    }

    try {
      console.log(`请求游戏玩家接口:`, api.defaults.baseURL + `/games/${gameId}/players`)
      const response = await api.get(`/games/${gameId}/players`)
      console.log(`游戏玩家响应:`, response.data)
      return handleResponse(response)
    } catch (error) {
      console.error(`获取游戏 ${gameId} 玩家信息失败:`, error.response ? error.response.data : error.message)
      // 如果服务器没有响应，返回空数组
      return []
    }
  },

  // 获取网关所有用户信息
  async getGatewayClients() {
    if (useMockData) {
      return mockGameService.getGatewayClients()
    }

    try {
      console.log('请求网关用户接口:', api.defaults.baseURL + '/gateway/clients')
      const response = await api.get('/gateway/clients')
      console.log('网关用户响应:', response.data)

      // 处理特定的响应格式，提取clients数组
      const responseData = handleResponse(response)
      if (responseData && responseData.clients && Array.isArray(responseData.clients)) {
        return responseData.clients
      } else if (Array.isArray(responseData)) {
        return responseData
      } else {
        console.warn('网关用户数据格式不符合预期:', responseData)
        return []
      }
    } catch (error) {
      console.error('获取网关用户信息失败:', error.response ? error.response.data : error.message)
      // 如果服务器没有响应，返回空数组
      return []
    }
  },

  // 获取历史在线人数数据
  async getPlayersHistory(period = '24h') {
    // 如果使用数据库
    if (useDB) {
      try {
        // 计算开始时间和结束时间
        const endTime = Date.now()
        let startTime = endTime

        // 根据时间段计算开始时间
        switch(period) {
          case '1h':
            startTime = endTime - (60 * 60 * 1000); // 1小时
            break;
          case '6h':
            startTime = endTime - (6 * 60 * 60 * 1000); // 6小时
            break;
          case '24h':
            startTime = endTime - (24 * 60 * 60 * 1000); // 24小时
            break;
          case '3d':
            startTime = endTime - (3 * 24 * 60 * 60 * 1000); // 3天
            break;
          case '7d':
            startTime = endTime - (7 * 24 * 60 * 60 * 1000); // 7天
            break;
          case '14d':
            startTime = endTime - (14 * 24 * 60 * 60 * 1000); // 14天
            break;
          case '30d':
            startTime = endTime - (30 * 24 * 60 * 60 * 1000); // 30天
            break;
          case '90d':
            startTime = endTime - (90 * 24 * 60 * 60 * 1000); // 90天
            break;
          default:
            startTime = endTime - (24 * 60 * 60 * 1000); // 默认24小时
        }

        // 首先检查API是否可用
        const apiAvailable = await dbService.checkApiAvailability(currentServerType)
        if (!apiAvailable) {
          console.warn(`${currentServerType}服务器API不可用，尝试使用本地存储数据`)
        }

        // 从数据库获取数据，传递当前服务器类型
        const historyData = await dbService.getPlayerStats(startTime, endTime, currentServerType)
        console.log(`从${currentServerType}服务器数据库获取了 ${historyData.length} 条历史数据`)

        // 如果没有数据，使用模拟数据
        if (historyData.length === 0) {
          console.warn(`${currentServerType}服务器数据库中没有历史数据，使用模拟数据`)
          return mockGameService.getPlayersHistory(period)
        }

        // 转换数据格式，确保值为整数
        const data = historyData.map(item => ({
          timestamp: item.timestamp,
          value: Math.round(item.count) // 确保值为整数
        }))

        // 计算变化百分比
        const change = dbService.calculateChangePercentage(historyData)
        console.log(`计算的变化百分比: ${change}, 类型: ${typeof change}`)

        // 返回格式化的数据
        return {
          period,
          data,
          currentValue: data[data.length - 1].value,
          change: Number(change), // 确保 change 是数字类型
          serverType: currentServerType
        }
      } catch (error) {
        console.error(`从${currentServerType}服务器数据库获取历史数据失败:`, error)
        // 如果数据库失败，使用模拟数据
        return mockGameService.getPlayersHistory(period)
      }
    }

    // 如果使用模拟数据
    if (useMockData) {
      return mockGameService.getPlayersHistory(period)
    }

    // 尝试从远程API获取数据
    try {
      console.log('请求历史在线人数接口:', api.defaults.baseURL + `/players/history?period=${period}`)
      const response = await api.get(`/players/history?period=${period}`, { timeout: 10000 })
      console.log('历史在线人数响应:', response.data)
      const result = handleResponse(response)

      // 确保 change 是数字类型
      if (result && typeof result.change === 'string') {
        result.change = Number(result.change)
      }

      return result
    } catch (error) {
      console.error('获取历史在线人数失败:', error.response ? error.response.data : error.message)
      // 如果服务器没有响应，返回模拟数据
      return mockGameService.getPlayersHistory(period)
    }
  }
}
