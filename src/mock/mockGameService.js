// 模拟延迟
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))

// 生成随机ID
const generateId = () => Math.floor(Math.random() * 10000).toString()

// 生成随机IP地址
const generateIp = () => {
  return `${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`
}

// 生成随机日期（最近7天内）
const generateDate = () => {
  const date = new Date()
  date.setDate(date.getDate() - Math.floor(Math.random() * 7))
  return date.toISOString()
}

// 生成随机状态
const generateStatus = () => {
  const statuses = ['active', 'waiting', 'closed']
  return statuses[Math.floor(Math.random() * statuses.length)]
}

// 生成随机连接状态
const generateConnectionStatus = () => {
  const statuses = ['connected', 'connecting', 'disconnected']
  return statuses[Math.floor(Math.random() * statuses.length)]
}

// 生成随机玩家
const generatePlayer = (roomId = null) => {
  const id = generateId()
  return {
    id,
    userId: id,
    username: `玩家${id}`,
    name: `玩家${id}`,
    ip: generateIp(),
    status: generateConnectionStatus(),
    connectionStatus: generateConnectionStatus(),
    roomId: roomId,
    connectedAt: generateDate(),
    createdAt: generateDate()
  }
}

// 生成随机房间
const generateRoom = () => {
  const id = generateId()
  const playerCount = Math.floor(Math.random() * 10)
  const createdAt = generateDate()

  return {
    id,
    gameId: Math.random() > 0.3 ? generateId() : null,
    playerCount,
    status: generateStatus(),
    createdAt,
    updatedAt: new Date().toISOString(),
    config: {
      maxPlayers: 10,
      gameMode: Math.random() > 0.5 ? 'battle' : 'cooperative',
      mapId: generateId(),
      timeLimit: Math.floor(Math.random() * 30) + 10
    },
    players: Array(playerCount).fill(null).map(() => generatePlayer(id))
  }
}

// 生成模拟数据
const generateMockData = () => {
  // 生成10-20个房间
  const roomCount = Math.floor(Math.random() * 10) + 10
  const rooms = Array(roomCount).fill(null).map(generateRoom)

  // 生成20-50个网关用户
  const clientCount = Math.floor(Math.random() * 30) + 20
  const gatewayClients = Array(clientCount).fill(null).map(generatePlayer)

  return {
    rooms,
    gatewayClients,
    playersCount: gatewayClients.length
  }
}

// 模拟数据
const mockData = generateMockData()

// 生成历史在线人数数据
const generateHistoryData = (period) => {
  let dataPoints = 0;
  let startTime = new Date();
  let baseValue = 4000000 + Math.floor(Math.random() * 500000); // 基础值约400万

  // 根据时间段设置数据点数量和起始时间
  switch(period) {
    case '24h':
      dataPoints = 24; // 每小时一个点
      startTime.setHours(startTime.getHours() - 24);
      break;
    case '7d':
      dataPoints = 7 * 24; // 每小时一个点
      startTime.setDate(startTime.getDate() - 7);
      break;
    case '30d':
      dataPoints = 30; // 每天一个点
      startTime.setDate(startTime.getDate() - 30);
      break;
    default:
      dataPoints = 24;
      startTime.setHours(startTime.getHours() - 24);
  }

  // 生成数据点
  const data = [];
  for (let i = 0; i < dataPoints; i++) {
    // 添加一些随机波动，但保持在一定范围内
    const variation = Math.random() * 0.05 - 0.025; // -2.5% 到 +2.5% 的波动
    // 确保值为整数，且最小为1
    const value = Math.max(1, Math.round(baseValue * (1 + variation)));

    let timestamp;
    if (period === '30d') {
      // 对于30天，每天一个点
      timestamp = new Date(startTime);
      timestamp.setDate(startTime.getDate() + i);
    } else {
      // 对于24小时和7天，每小时一个点
      timestamp = new Date(startTime);
      timestamp.setHours(startTime.getHours() + i);
    }

    data.push({
      timestamp: timestamp.getTime(),
      value: value
    });

    // 为下一个点更新基础值（有小趋势）
    baseValue = value;
  }

  return {
    period,
    data,
    currentValue: data[data.length - 1].value,
    change: ((data[data.length - 1].value - data[0].value) / data[0].value * 100).toFixed(2)
  };
};

// 模拟游戏服务
export const mockGameService = {
  // 获取所有房间信息
  async getAllRooms() {
    await delay(800)
    return mockData.rooms
  },

  // 获取特定房间的信息
  async getRoomById(roomId) {
    await delay(500)
    const room = mockData.rooms.find(r => r.id === roomId)
    if (!room) {
      throw new Error('房间不存在')
    }
    return room
  },

  // 获取特定游戏节点的玩家信息
  async getGamePlayers(gameId) {
    await delay(700)
    const players = []
    mockData.rooms.forEach(room => {
      if (room.gameId === gameId) {
        players.push(...room.players)
      }
    })
    return players
  },

  // 获取网关所有用户信息
  async getGatewayClients() {
    await delay(600)
    return mockData.gatewayClients
  },

  // 获取历史在线人数数据
  async getPlayersHistory(period = '24h') {
    await delay(1000)
    return generateHistoryData(period)
  }
}
