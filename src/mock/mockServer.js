const mockFiles = [
  {
    name: '文档.pdf',
    type: 'file',
    size: 1024 * 1024 * 2.5 // 2.5MB
  },
  {
    name: '图片.jpg',
    type: 'file',
    size: 1024 * 512 // 512KB
  },
  {
    name: '我的文件夹',
    type: 'directory'
  }
]

// 模拟 API 延迟
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))

// 覆盖 fileService 中的方法
export const mockFileService = {
  async listFiles() {
    await delay(800) // 模拟网络延迟
    return mockFiles
  },

  async createFolder(path, name) {
    await delay(500)
    mockFiles.push({
      name,
      type: 'directory'
    })
    return { success: true }
  },

  async deleteFile(path) {
    await delay(500)
    const fileName = path.split('/').pop()
    const index = mockFiles.findIndex(file => file.name === fileName)
    if (index > -1) {
      mockFiles.splice(index, 1)
    }
    return { success: true }
  },

  async uploadFile(path, file) {
    await delay(1000)
    mockFiles.push({
      name: file.name,
      type: 'file',
      size: file.size
    })
    return { success: true }
  },

  getDownloadUrl(path) {
    return '#' // 模拟下载链接
  }
} 