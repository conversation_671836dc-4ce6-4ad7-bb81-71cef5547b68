<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
    <header class="bg-white dark:bg-gray-800 shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center space-x-8">
            <h1 class="text-2xl font-bold text-primary-600 dark:text-primary-400">管理后台</h1>

            <!-- 导航菜单 -->
            <nav class="flex space-x-4">
              <router-link
                to="/"
                class="px-3 py-2 rounded-md text-sm font-medium transition-colors"
                :class="$route.path === '/' ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300' : 'text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'"
              >
                文件管理
              </router-link>
              <router-link
                to="/game"
                class="px-3 py-2 rounded-md text-sm font-medium transition-colors"
                :class="$route.path === '/game' ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300' : 'text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'"
              >
                游戏管理
              </router-link>
              <router-link
                to="/editor"
                class="px-3 py-2 rounded-md text-sm font-medium transition-colors"
                :class="$route.path === '/editor' ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300' : 'text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'"
              >
                模型编辑器
              </router-link>
            </nav>
          </div>

          <div class="flex items-center space-x-4">
            <button
              @click="toggleTheme"
              class="p-2 rounded-lg text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"
            >
              <sun-icon v-if="isDark" class="h-6 w-6" />
              <moon-icon v-else class="h-6 w-6" />
            </button>
          </div>
        </div>
      </div>
    </header>

    <main :class="isFullScreenRoute ? 'h-[calc(100vh-4rem)]' : 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8'">
      <slot></slot>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { SunIcon, MoonIcon } from '@heroicons/vue/24/outline'

const $route = useRoute()

// 判断是否是需要全屏显示的路由
const isFullScreenRoute = computed(() => {
  return $route.path === '/editor'
})

const isDark = ref(false)

const toggleTheme = () => {
  isDark.value = !isDark.value
  document.documentElement.classList.toggle('dark')
  localStorage.setItem('theme', isDark.value ? 'dark' : 'light')
}

onMounted(() => {
  const theme = localStorage.getItem('theme')
  isDark.value = theme === 'dark'
  if (isDark.value) {
    document.documentElement.classList.add('dark')
  }
})
</script>