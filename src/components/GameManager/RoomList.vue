<template>
  <div>
    <div class="mb-4 flex justify-between items-center">
      <h2 class="text-lg font-medium text-gray-900 dark:text-white">房间列表</h2>
      <div class="relative">
        <input
          type="text"
          placeholder="搜索房间..."
          class="input pl-10"
          v-model="searchQuery"
        />
        <MagnifyingGlassIcon class="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="py-10 flex justify-center">
      <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-primary-500"></div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!filteredRooms.length" class="py-10 text-center">
      <p class="text-gray-500 dark:text-gray-400">暂无房间数据</p>
    </div>

    <!-- 房间列表 -->
    <div v-else class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead class="bg-gray-50 dark:bg-gray-700">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              房间ID
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              端口
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              玩家数量
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              状态
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              最后心跳
            </th>
            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              操作
            </th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          <tr v-for="room in filteredRooms" :key="room.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white truncate" style="max-width: 200px;">
              {{ room.id }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
              {{ room.port || '无' }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
              {{ room.playerCount || 0 }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span
                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                :class="getRoomStatusClass(room)"
              >
                {{ getRoomStatusText(room) }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
              {{ formatHeartbeatTime(room.lastHeartbeat) }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <button
                @click="$emit('view-room', room)"
                class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300"
              >
                查看详情
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { MagnifyingGlassIcon } from '@heroicons/vue/24/outline'

// 接收属性
const props = defineProps({
  rooms: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// 定义事件
const emit = defineEmits(['view-room'])

// 状态
const searchQuery = ref('')

// 计算属性 - 过滤房间
const filteredRooms = computed(() => {
  if (!searchQuery.value) return props.rooms

  const query = searchQuery.value.toLowerCase()
  return props.rooms.filter(room => {
    try {
      // 防止空值异常
      const roomId = room.id ? room.id.toString() : ''
      const port = room.port ? room.port.toString() : ''

      return roomId.toLowerCase().includes(query) || port.includes(query)
    } catch (error) {
      console.error('过滤房间数据异常:', error, room)
      return false
    }
  })
})

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知'

  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 格式化心跳时间
const formatHeartbeatTime = (timestamp) => {
  if (!timestamp) return '未知'

  // 如果是13位数字，则是毫秒级时间戳
  const date = new Date(Number(timestamp))
  if (isNaN(date.getTime())) return '无效时间'

  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 获取房间状态样式
const getRoomStatusClass = (room) => {
  // 检查最后心跳时间，如果距离现在超过30秒，则认为房间已离线
  if (room.lastHeartbeat) {
    const now = Date.now()
    const lastHeartbeat = Number(room.lastHeartbeat)
    const diff = now - lastHeartbeat

    if (diff > 30000) { // 30秒
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
    }
  }

  // 根据玩家数量判断房间状态
  if (room.playerCount > 0) {
    return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
  } else {
    return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
  }
}

// 获取房间状态文本
const getRoomStatusText = (room) => {
  // 检查最后心跳时间，如果距离现在超过30秒，则认为房间已离线
  if (room.lastHeartbeat) {
    const now = Date.now()
    const lastHeartbeat = Number(room.lastHeartbeat)
    const diff = now - lastHeartbeat

    if (diff > 30000) { // 30秒
      return '离线'
    }
  }

  // 根据玩家数量判断房间状态
  if (room.playerCount > 0) {
    return '活跃'
  } else {
    return '空闲'
  }
}
</script>
