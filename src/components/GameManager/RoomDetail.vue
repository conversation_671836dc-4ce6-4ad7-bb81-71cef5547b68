<template>
  <div>
    <!-- 加载状态 -->
    <div v-if="loading" class="py-10 flex justify-center">
      <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-primary-500"></div>
    </div>

    <div v-else>
      <!-- 房间基本信息 -->
      <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">房间基本信息</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div class="bg-white dark:bg-gray-800 p-3 rounded shadow-sm">
            <p class="text-sm text-gray-500 dark:text-gray-400">房间ID</p>
            <p class="font-medium text-gray-900 dark:text-white">{{ room.id }}</p>
          </div>
          <div class="bg-white dark:bg-gray-800 p-3 rounded shadow-sm">
            <p class="text-sm text-gray-500 dark:text-gray-400">端口</p>
            <p class="font-medium text-gray-900 dark:text-white">{{ room.port || '无' }}</p>
          </div>
          <div class="bg-white dark:bg-gray-800 p-3 rounded shadow-sm">
            <p class="text-sm text-gray-500 dark:text-gray-400">房间状态</p>
            <p class="font-medium">
              <span
                class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
                :class="getRoomStatusClass(room)"
              >
                {{ getRoomStatusText(room) }}
              </span>
            </p>
          </div>
          <div class="bg-white dark:bg-gray-800 p-3 rounded shadow-sm">
            <p class="text-sm text-gray-500 dark:text-gray-400">玩家数量</p>
            <p class="font-medium text-gray-900 dark:text-white">{{ room.playerCount || 0 }}</p>
          </div>
          <div class="bg-white dark:bg-gray-800 p-3 rounded shadow-sm">
            <p class="text-sm text-gray-500 dark:text-gray-400">最后心跳</p>
            <p class="font-medium text-gray-900 dark:text-white">{{ formatHeartbeatTime(room.lastHeartbeat) }}</p>
          </div>
        </div>
      </div>

      <!-- 房间配置信息 -->
      <div v-if="room.config" class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">房间配置</h3>
        <div class="bg-white dark:bg-gray-800 p-4 rounded shadow-sm">
          <pre class="text-sm text-gray-700 dark:text-gray-300 overflow-auto">{{ formatJson(room.config) }}</pre>
        </div>
      </div>

      <!-- 玩家列表 -->
      <div v-if="room.players && room.players.length" class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">房间玩家</h3>
        <PlayerList :players="room.players" :loading="false" />
      </div>

      <!-- 无玩家提示 -->
      <div v-else-if="room.players && !room.players.length" class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg text-center py-8">
        <p class="text-gray-500 dark:text-gray-400">该房间暂无玩家</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue'
import PlayerList from './PlayerList.vue'

// 接收属性
const props = defineProps({
  room: {
    type: Object,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知'

  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 格式化JSON
const formatJson = (json) => {
  if (!json) return '{}'
  try {
    return JSON.stringify(json, null, 2)
  } catch (e) {
    return String(json)
  }
}

// 格式化心跳时间
const formatHeartbeatTime = (timestamp) => {
  if (!timestamp) return '未知'

  // 如果是13位数字，则是毫秒级时间戳
  const date = new Date(Number(timestamp))
  if (isNaN(date.getTime())) return '无效时间'

  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 获取房间状态样式
const getRoomStatusClass = (room) => {
  // 检查最后心跳时间，如果距离现在超过30秒，则认为房间已离线
  if (room.lastHeartbeat) {
    const now = Date.now()
    const lastHeartbeat = Number(room.lastHeartbeat)
    const diff = now - lastHeartbeat

    if (diff > 30000) { // 30秒
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
    }
  }

  // 根据玩家数量判断房间状态
  if (room.playerCount > 0) {
    return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
  } else {
    return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
  }
}

// 获取房间状态文本
const getRoomStatusText = (room) => {
  // 检查最后心跳时间，如果距离现在超过30秒，则认为房间已离线
  if (room.lastHeartbeat) {
    const now = Date.now()
    const lastHeartbeat = Number(room.lastHeartbeat)
    const diff = now - lastHeartbeat

    if (diff > 30000) { // 30秒
      return '离线'
    }
  }

  // 根据玩家数量判断房间状态
  if (room.playerCount > 0) {
    return '活跃'
  } else {
    return '空闲'
  }
}
</script>
