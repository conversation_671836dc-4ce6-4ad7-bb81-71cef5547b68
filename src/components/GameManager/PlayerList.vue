<template>
  <div>
    <div class="mb-4 flex justify-between items-center">
      <h2 v-if="title" class="text-lg font-medium text-gray-900 dark:text-white">{{ title }}</h2>
      <div class="relative">
        <input
          type="text"
          placeholder="搜索玩家..."
          class="input pl-10"
          v-model="searchQuery"
        />
        <MagnifyingGlassIcon class="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="py-10 flex justify-center">
      <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-primary-500"></div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!filteredPlayers.length" class="py-10 text-center">
      <p class="text-gray-500 dark:text-gray-400">暂无玩家数据</p>
    </div>

    <!-- 玩家列表 -->
    <div v-else class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead class="bg-gray-50 dark:bg-gray-700">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              玩家ID
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              BTC地址
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              网关ID
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              状态
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              平均延迟
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              最大延迟
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              连接时间
            </th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          <tr v-for="player in filteredPlayers" :key="player.id || player.userId" class="hover:bg-gray-50 dark:hover:bg-gray-700">
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
              <span :title="player.id || player.userId || ''">
                {{ formatPlayerId(player.id || player.userId) || '未知' }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400" style="max-width: 200px;">
              <div class="flex items-center space-x-1">
                <span :title="player.btcAddress || ''" class="flex-1">
                  {{ formatBtcAddressWithNote(player.btcAddress) || '未知' }}
                </span>
                <button
                  v-if="player.btcAddress"
                  @click.stop="openNoteDialog(player.btcAddress)"
                  class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1"
                  title="添加/编辑备注"
                >
                  <PencilIcon class="h-4 w-4" />
                </button>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400" style="max-width: 150px;">
              <span :title="player.gateway_id || ''">
                {{ formatGatewayId(player.gateway_id) || '未知' }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span
                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                :class="getConnectionStatusClass('connected')"
              >
                {{ getConnectionStatusText('connected') }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
              {{ formatPingValue(getPingAvg(player)) }} ms
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
              {{ formatPingValue(getPingMax(player)) }} ms
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
              {{ formatJoinTime(player.joinTime) }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- BTC地址备注对话框 -->
    <el-dialog
      v-model="showNoteDialog"
      title="BTC地址备注"
      width="500px"
      destroy-on-close
    >
      <div v-if="currentBtcAddress" class="space-y-4">
        <div>
          <p class="text-sm text-gray-500 dark:text-gray-400 mb-1">地址</p>
          <p class="text-sm font-medium text-gray-900 dark:text-white break-all">{{ currentBtcAddress }}</p>
        </div>

        <div>
          <p class="text-sm text-gray-500 dark:text-gray-400 mb-1">备注</p>
          <el-input
            v-model="currentNote"
            type="text"
            placeholder="输入备注信息"
            maxlength="50"
            show-word-limit
          />
        </div>

        <div class="flex justify-between pt-4">
          <el-button
            v-if="currentNote"
            @click="deleteNote"
            type="danger"
            plain
          >
            删除备注
          </el-button>
          <div class="flex-1"></div>
          <div class="space-x-2">
            <el-button @click="showNoteDialog = false">取消</el-button>
            <el-button type="primary" @click="saveNote">保存</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { MagnifyingGlassIcon, PencilIcon } from '@heroicons/vue/24/outline'
import { ElMessage, ElDialog, ElButton, ElInput } from 'element-plus'
import { storageService } from '../../services/storageService'

// 接收属性
const props = defineProps({
  players: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  }
})

// 状态
const searchQuery = ref('')
const showNoteDialog = ref(false)
const currentBtcAddress = ref('')
const currentNote = ref('')

// 计算属性 - 过滤玩家
const filteredPlayers = computed(() => {
  if (!searchQuery.value) return props.players

  const query = searchQuery.value.toLowerCase()
  return props.players.filter(player => {
    try {
      // 防止空值异常
      const id = player.id || player.userId || ''
      const btcAddress = player.btcAddress || ''
      const gatewayId = player.gateway_id || ''
      const note = getBtcAddressNote(btcAddress)

      return (id ? id.toString() : '').toLowerCase().includes(query) ||
             (btcAddress ? btcAddress.toString() : '').toLowerCase().includes(query) ||
             (gatewayId ? gatewayId.toString() : '').toLowerCase().includes(query) ||
             note.toLowerCase().includes(query)
    } catch (error) {
      console.error('过滤玩家数据异常:', error, player)
      return false
    }
  })
})

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知'

  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 格式化加入时间戳
const formatJoinTime = (timestamp) => {
  if (!timestamp) return '未知'

  // 如果是13位数字，则是毫秒级时间戳
  const date = new Date(Number(timestamp))
  if (isNaN(date.getTime())) return '无效时间'

  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 获取玩家平均延迟
const getPingAvg = (player) => {
  if (!player || !player.pingData) return 0

  // 如果有avgPing属性则直接返回
  if (typeof player.pingData.avgPing === 'number') {
    return player.pingData.avgPing
  }

  // 如果没有avgPing但有totalPing和pingTime，则计算平均值
  if (typeof player.pingData.totalPing === 'number' && typeof player.pingData.pingTime === 'number' && player.pingData.pingTime > 0) {
    return Math.round(player.pingData.totalPing / player.pingData.pingTime)
  }

  return 0
}

// 获取玩家最大延迟
const getPingMax = (player) => {
  if (!player || !player.pingData) return 0
  return player.pingData.maxPing || 0
}

// 格式化延迟值
const formatPingValue = (value) => {
  // 如果值大于99999，可能是初始值，显示为未知
  if (value >= 99999) return '未知'
  if (value <= 0) return '0'
  return value.toString()
}

// 格式化BTC地址，中间省略
const formatBtcAddress = (address) => {
  if (!address) return ''
  if (address.length <= 20) return address

  // 显示前10个字符和后10个字符，中间用...代替
  try {
    console.log('格式化地址:', address, typeof address)
    const addressStr = String(address)
    const start = addressStr.substring(0, 10)
    const end = addressStr.substring(addressStr.length - 10)
    return `${start}...${end}`
  } catch (error) {
    console.error('格式化地址失败:', error, address)
    return String(address)
  }
}

// 格式化BTC地址，带备注
const formatBtcAddressWithNote = (address) => {
  if (!address) return ''

  const note = getBtcAddressNote(address)
  if (note) {
    return `${note} (${formatBtcAddress(address)})`
  }

  return formatBtcAddress(address)
}

// 获取BTC地址备注
const getBtcAddressNote = (address) => {
  if (!address) return ''
  return storageService.getBtcAddressNote(address)
}

// 格式化网关ID，中间省略
const formatGatewayId = (id) => {
  if (!id) return ''
  if (id.length <= 16) return id

  // 显示前8个字符和后8个字符，中间用...代替
  try {
    const idStr = String(id)
    const start = idStr.substring(0, 8)
    const end = idStr.substring(idStr.length - 8)
    return `${start}...${end}`
  } catch (error) {
    console.error('格式化网关ID失败:', error, id)
    return String(id)
  }
}

// 格式化玩家ID，中间省略
const formatPlayerId = (id) => {
  if (!id) return ''
  if (id.length <= 14) return id

  // 显示前7个字符和后7个字符，中间用...代替
  try {
    const idStr = String(id)
    const start = idStr.substring(0, 7)
    const end = idStr.substring(idStr.length - 7)
    return `${start}...${end}`
  } catch (error) {
    console.error('格式化玩家ID失败:', error, id)
    return String(id)
  }
}

// 获取连接状态样式
const getConnectionStatusClass = (status) => {
  switch (status) {
    case 'connected':
    case 'online':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
    case 'connecting':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
    case 'disconnected':
    case 'offline':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
  }
}

// 获取连接状态文本
const getConnectionStatusText = (status) => {
  switch (status) {
    case 'connected':
    case 'online':
      return '在线'
    case 'connecting':
      return '连接中'
    case 'disconnected':
    case 'offline':
      return '离线'
    default:
      return '未知'
  }
}

// 打开备注对话框
const openNoteDialog = (address) => {
  currentBtcAddress.value = address
  currentNote.value = storageService.getBtcAddressNote(address)
  showNoteDialog.value = true
}

// 保存备注
const saveNote = () => {
  if (!currentBtcAddress.value) return

  const success = storageService.saveBtcAddressNote(currentBtcAddress.value, currentNote.value.trim())
  if (success) {
    ElMessage.success('备注保存成功')
    showNoteDialog.value = false
  } else {
    ElMessage.error('备注保存失败')
  }
}

// 删除备注
const deleteNote = () => {
  if (!currentBtcAddress.value) return

  const success = storageService.deleteBtcAddressNote(currentBtcAddress.value)
  if (success) {
    ElMessage.success('备注删除成功')
    currentNote.value = ''
    showNoteDialog.value = false
  } else {
    ElMessage.error('备注删除失败')
  }
}
</script>
