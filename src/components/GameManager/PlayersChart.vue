<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
    <div class="flex items-center justify-between mb-4">
      <div>
        <h2 class="text-lg font-medium text-gray-900 dark:text-white">在线人数统计</h2>
        <div class="flex items-center mt-1">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">
            {{ formatNumber(Math.round(currentValue)) }}
          </p>
          <span
            class="ml-2 px-2 py-1 text-sm rounded-full"
            :class="changeClass"
          >
            {{ Number(change) > 0 ? '+' : '' }}{{ Number(change).toFixed(2) }}%
          </span>
          <button
            @click="$emit('refresh')"
            class="ml-3 p-1 text-gray-500 hover:text-primary-600 dark:text-gray-400 dark:hover:text-primary-400"
            title="刷新数据"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        </div>
      </div>

      <!-- 时间范围选择 -->
      <div class="relative">
        <button
          @click="showPeriodDropdown = !showPeriodDropdown"
          class="px-3 py-1 text-sm rounded-md flex items-center space-x-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600"
        >
          <span>{{ getCurrentPeriodLabel() }}</span>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        </button>

        <!-- 下拉菜单 -->
        <div
          v-if="showPeriodDropdown"
          class="absolute right-0 mt-1 bg-white dark:bg-gray-800 rounded-md shadow-lg z-10 py-1 w-48 border border-gray-200 dark:border-gray-700"
        >
          <div class="max-h-60 overflow-y-auto">
            <button
              v-for="option in periodOptions"
              :key="option.value"
              @click="changePeriod(option.value); showPeriodDropdown = false"
              class="block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700"
              :class="selectedPeriod === option.value
                ? 'bg-primary-50 text-primary-700 dark:bg-primary-900 dark:text-primary-300'
                : 'text-gray-700 dark:text-gray-300'"
            >
              {{ option.label }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表容器 -->
    <div class="h-64 relative">
      <!-- 加载状态 -->
      <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-white/50 dark:bg-gray-800/50">
        <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-primary-500"></div>
      </div>

      <!-- 图表 -->
      <canvas ref="chartRef"></canvas>
    </div>

    <!-- 不再需要额外的时间轴标签，因为图表已经显示了X轴标签 -->
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
// 修改导入方式，避免 vite 构建时的解析问题
import { Chart, registerables } from 'chart.js'
// 注册所有需要的组件
Chart.register(...registerables)

// 接收属性
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  period: {
    type: String,
    default: '24h'
  },
  loading: {
    type: Boolean,
    default: false
  },
  currentValue: {
    type: Number,
    default: 0
  },
  change: {
    type: Number,
    default: 0,
    validator: function(value) {
      // 如果收到字符串，尝试转换为数字
      if (typeof value === 'string') {
        console.warn('PlayersChart: change prop received string value, expected number');
        return !isNaN(Number(value));
      }
      return true;
    }
  }
})

// 事件
const emit = defineEmits(['period-change', 'refresh'])

// 状态
const chartRef = ref(null)
const chart = ref(null)
const selectedPeriod = ref(props.period)
const showPeriodDropdown = ref(false)

// 获取当前选中的时间段标签
const getCurrentPeriodLabel = () => {
  const option = periodOptions.find(opt => opt.value === selectedPeriod.value)
  return option ? option.label : '24小时'
}

// 时间段选项
const periodOptions = [
  { label: '1小时', value: '1h' },
  { label: '6小时', value: '6h' },
  { label: '24小时', value: '24h' },
  { label: '3天', value: '3d' },
  { label: '7天', value: '7d' },
  { label: '14天', value: '14d' },
  { label: '30天', value: '30d' },
  { label: '90天', value: '90d' }
]

// 计算属性
const changeClass = computed(() => {
  // 确保 change 被当作数字处理
  const changeValue = Number(props.change)
  return changeValue > 0
    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
})

// 时间轴标签
const timeAxisLabels = computed(() => {
  if (!props.data || props.data.length === 0) return []

  // 根据数据点数量决定显示多少个标签
  const dataLength = props.data.length

  // 如果数据点太少，就全部显示
  if (dataLength <= 5) {
    return props.data.map(point => formatTime(point.timestamp))
  }

  // 否则选择均匀分布的几个点
  const labels = []
  const step = Math.max(1, Math.floor(dataLength / 5)) // 最多显示5个标签

  for (let i = 0; i < dataLength; i += step) {
    if (labels.length < 5) { // 限制最多5个标签
      labels.push(formatTime(props.data[i].timestamp))
    }
  }

  // 确保包含最后一个点
  const lastLabel = formatTime(props.data[dataLength - 1].timestamp)
  if (labels[labels.length - 1] !== lastLabel) {
    labels.push(lastLabel)
  }

  return labels
})

// 格式化数字
const formatNumber = (num) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(2) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 计算Y轴最小值
const calculateYAxisMin = (data) => {
  if (!data || data.length === 0) return 0

  try {
    // 找出数据中的最小值
    const minValue = Math.min(...data.map(point => point.value))

    // 如果最小值已经是整数，则减1；否则向下取整
    const floorValue = Math.floor(minValue)

    // 确保最小值不小于0
    return Math.max(0, floorValue - 1)
  } catch (error) {
    console.error('计算Y轴最小值出错:', error)
    return 0
  }
}

// 计算Y轴最大值
const calculateYAxisMax = (data) => {
  if (!data || data.length === 0) return 10

  try {
    // 找出数据中的最大值
    const maxValue = Math.max(...data.map(point => point.value))

    // 向上取整并加1
    const ceilValue = Math.ceil(maxValue)

    // 如果数据范围太小，确保至少有一定的范围
    const minValue = Math.min(...data.map(point => point.value))
    const range = ceilValue - Math.floor(minValue)

    // 最小范围为5
    if (range < 5) {
      return ceilValue + (5 - range)
    }

    return ceilValue + 1
  } catch (error) {
    console.error('计算Y轴最大值出错:', error)
    return 10
  }
}

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp)

  // 获取格式化的小时和分钟
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')

  // 根据不同的时间范围使用不同的格式
  switch(selectedPeriod.value) {
    case '1h':
      // 显示小时和分钟
      return `${hours}:${minutes}`

    case '6h':
      // 显示小时和分钟，确保15分钟间隔
      return `${hours}:${minutes}`

    case '24h':
      // 显示小时和分钟，确保15分钟间隔
      return `${hours}:${minutes}`

    case '3d':
    case '7d':
      // 显示日期、小时和分钟
      return `${month}/${day} ${hours}:${minutes}`

    case '14d':
      // 显示日期和小时
      return `${month}/${day} ${hours}:00`

    case '30d':
    case '90d':
      // 显示月/日
      return `${month}/${day}`

    default:
      return `${hours}:${minutes}`
  }
}

// 更改时间段
const changePeriod = (period) => {
  selectedPeriod.value = period
  emit('period-change', period)
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  // 销毁旧图表
  if (chart.value) {
    chart.value.destroy()
  }

  // 处理数据，确保所有值都是整数
  const displayData = props.data.map(point => ({
    ...point,
    value: Math.round(point.value) // 确保值为整数
  }))

  // 准备图表数据
  const chartData = {
    labels: displayData.map(point => formatTime(point.timestamp)),
    datasets: [{
      label: '在线人数',
      data: displayData.map(point => point.value),
      borderColor: '#0ea5e9',
      backgroundColor: 'rgba(14, 165, 233, 0.1)',
      borderWidth: 2,
      fill: true,
      tension: 0.4,
      pointRadius: 0,
      pointHoverRadius: 4
    }]
  }

  // 创建图表
  const ctx = chartRef.value.getContext('2d')
  chart.value = new Chart(ctx, {
    type: 'line',
    data: chartData,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          mode: 'index',
          intersect: false,
          callbacks: {
            title: function(tooltipItems) {
              // 显示完整的日期时间
              const dataIndex = tooltipItems[0].dataIndex
              const timestamp = displayData[dataIndex]?.timestamp
              if (!timestamp) return ''

              const date = new Date(timestamp)
              // 确保显示年月日时分，格式：2023-04-30 11:45
              return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
              })
            },
            label: function(context) {
              // 确保显示整数
              return '在线人数: ' + formatNumber(Math.round(context.raw))
            }
          }
        }
      },
      scales: {
        x: {
          display: true, // 显示X轴
          grid: {
            display: false
          },
          ticks: {
            // 根据数据点数量自动调整显示的标签数量
            autoSkip: true,
            maxTicksLimit: 8, // 增加标签数量，以便更好地显示15分钟间隔
            callback: function(value, index, values) {
              // 自定义X轴标签显示
              const timestamp = displayData[index]?.timestamp
              if (!timestamp) return ''

              // 格式化时间，确保显示分钟
              const date = new Date(timestamp)
              const hours = date.getHours().toString().padStart(2, '0')
              const minutes = date.getMinutes().toString().padStart(2, '0')

              // 根据不同的时间范围使用不同的格式
              switch(selectedPeriod.value) {
                case '1h':
                  return `${hours}:${minutes}`
                case '6h':
                  return `${hours}:${minutes}`
                case '24h':
                  // 对于24小时，只在整点和15/30/45分时显示分钟
                  return minutes === '00' ? `${hours}:00` : `${hours}:${minutes}`
                case '3d':
                case '7d':
                  // 对于3天和7天，只显示日期和小时
                  const month = (date.getMonth() + 1).toString().padStart(2, '0')
                  const day = date.getDate().toString().padStart(2, '0')
                  return `${month}/${day} ${hours}:00`
                default:
                  // 对于更长的时间范围，只显示日期
                  const m = (date.getMonth() + 1).toString().padStart(2, '0')
                  const d = date.getDate().toString().padStart(2, '0')
                  return `${m}/${d}`
              }
            }
          }
        },
        y: {
          // 设置Y轴范围
          min: calculateYAxisMin(displayData),
          max: calculateYAxisMax(displayData),
          beginAtZero: false,
          grid: {
            color: 'rgba(160, 174, 192, 0.1)'
          },
          ticks: {
            // 确保只显示整数刻度
            stepSize: 1,
            precision: 0,
            callback: function(value) {
              // 只返回整数值
              if (Number.isInteger(value)) {
                return formatNumber(Math.floor(value))
              }
              return ''
            }
          }
        }
      }
    }
  })
}

// 监听数据变化
watch(() => props.data, () => {
  initChart()
}, { deep: true })

// 点击外部关闭下拉菜单
const closeDropdownOnOutsideClick = (event) => {
  // 如果点击的不是下拉菜单内的元素，则关闭下拉菜单
  if (showPeriodDropdown.value) {
    showPeriodDropdown.value = false
  }
}

// 组件挂载
onMounted(() => {
  initChart()
  // 添加全局点击事件监听器
  document.addEventListener('click', closeDropdownOnOutsideClick)
})

// 组件卸载
onUnmounted(() => {
  // 移除全局点击事件监听器
  document.removeEventListener('click', closeDropdownOnOutsideClick)
})
</script>
