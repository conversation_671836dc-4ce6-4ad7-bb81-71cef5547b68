/**
 * @fileoverview 文件管理核心逻辑 Composable
 */
import { ref, computed, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
    listObjectsInDirectory,
    createFolderPlaceholder,
    deleteObject,
    deleteObjectsByPrefix,
    getPresignedUrlForDownload
} from '../../services/obsService'
import { requestManager } from './requestManager'
import { debounce } from './utils'

export function useFileManager() {
    // 状态
    const items = ref([])
    const currentPath = ref('website_public')
    const loading = ref(false)
    const currentLoadController = ref(null)

    // 计算属性
    const pathParts = computed(() => {
        if (!currentPath.value || currentPath.value === 'website_public') {
            return []
        }
        const path = currentPath.value.startsWith('website_public/')
            ? currentPath.value.substring('website_public/'.length)
            : currentPath.value
        return path.split('/').filter(Boolean)
    })

    // 核心文件加载函数
    const _loadFiles = async (retryCount = 1) => {
        if (currentLoadController.value) {
            currentLoadController.value.abort()
        }

        try {
            loading.value = true
            currentLoadController.value = new AbortController()

            let obsPrefix = currentPath.value
            if (obsPrefix && !obsPrefix.endsWith('/')) {
                obsPrefix += '/'
            }
            const result = await listObjectsInDirectory(obsPrefix || '', null, 1000, currentLoadController.value.signal)

            const newItems = []
            const basePathForNameExtraction = obsPrefix

            result.files.forEach(file => {
                let name = file.key
                if (basePathForNameExtraction && file.key.startsWith(basePathForNameExtraction)) {
                    name = file.key.substring(basePathForNameExtraction.length)
                }
                if (name && name !== '/') {
                    newItems.push({
                        name: name,
                        key: file.key,
                        type: 'file',
                        size: file.size,
                        lastModified: file.lastModified,
                    })
                }
            })

            result.folders.forEach(folder => {
                let name = folder.prefix
                if (basePathForNameExtraction && folder.prefix.startsWith(basePathForNameExtraction)) {
                    name = folder.prefix.substring(basePathForNameExtraction.length)
                }
                name = name.endsWith('/') ? name.slice(0, -1) : name

                if (name) {
                    newItems.push({
                        name: name,
                        key: folder.prefix.slice(0, -1),
                        type: 'directory',
                    })
                }
            })

            items.value = newItems
        } catch (error) {
            if (error.name === 'AbortError' || error.message === '操作被取消') {
                console.log('文件列表加载被取消')
                return
            }
            console.error('加载文件列表失败:', error)
            if (retryCount > 0) {
                ElMessage.warning('加载失败，正在重试...')
                setTimeout(() => _loadFiles(retryCount - 1), 1000)
                return
            }
            ElMessage.error('加载文件列表失败')
        } finally {
            loading.value = false
            currentLoadController.value = null
        }
    }

    // 使用RequestManager的loadFiles函数
    const loadFiles = async (retryCount = 1) => {
        const requestKey = `load_${currentPath.value}`
        try {
            await requestManager.execute(requestKey, _loadFiles, retryCount)
        } catch (error) {
            console.error('RequestManager执行失败:', error)
        }
    }

    const debouncedLoadFiles = debounce(loadFiles, 300)

    // 导航函数
    const navigateTo = (path) => {
        if (currentPath.value === path) return
        currentPath.value = path
        nextTick(() => {
            debouncedLoadFiles().catch(e => console.error(e))
        })
    }

    const navigateToFolder = (folder) => {
        navigateTo(`${currentPath.value}/${folder.name}`)
    }

    const navigateBack = () => {
        if (!currentPath.value || currentPath.value === 'website_public') return
        const parts = currentPath.value.split('/')
        parts.pop()
        const newPath = parts.join('/') || 'website_public'
        navigateTo(newPath)
    }

    const navigateToRoot = () => {
        navigateTo('website_public')
    }

    const navigateToPathByIndex = (index) => {
        const pathSegments = pathParts.value.slice(0, index + 1)
        const newPath = pathSegments.length > 0
            ? `website_public/${pathSegments.join('/')}`
            : 'website_public'
        navigateTo(newPath)
    }

    // 文件操作
    const createFolder = async (folderName) => {
        const exists = items.value.some(item => item.name === folderName);
        if (exists) {
            ElMessage.error(`名为 "${folderName}" 的文件或文件夹已存在。`);
            return false;
        }
        loading.value = true
        try {
            let folderKey = `${currentPath.value}/${folderName}/`
            const result = await createFolderPlaceholder(folderKey)
            if (result.status === 'success') {
                ElMessage.success(`文件夹 ${folderName} 创建成功`)
                await loadFiles()
                return true
            } else {
                ElMessage.error(`创建文件夹 ${folderName} 失败: ${result.error || '未知错误'}`)
                return false
            }
        } catch (error) {
            ElMessage.error(`创建文件夹 ${folderName} 时发生错误: ${error.message || '未知错误'}`)
            return false
        } finally {
            loading.value = false
        }
    }

    const downloadFile = async (file) => {
        loading.value = true;
        try {
            const result = await getPresignedUrlForDownload(file.key);
            if (result.status === 'success' && result.url) {
                const link = document.createElement('a');
                link.href = result.url;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } else {
                ElMessage.error(`获取下载链接失败: ${result.error || '未知错误'}`);
            }
        } catch (error) {
            ElMessage.error(`下载文件时发生错误: ${error.message || '未知错误'}`);
        } finally {
            loading.value = false;
        }
    }

    const deleteItem = async (item) => {
        const isDirectory = item.type === 'directory';
        const confirmMessage = `确定要删除 ${item.name} 吗？${isDirectory ? '这将删除该目录下的所有内容！' : ''}`;
        if (!confirm(confirmMessage)) return;

        loading.value = true;
        try {
            if (isDirectory) {
                const prefixToDelete = item.key.endsWith('/') ? item.key : item.key + '/';
                await deleteObjectsByPrefix(prefixToDelete);
            } else {
                await deleteObject(item.key);
            }
            ElMessage.success(`${item.name} 删除成功`);
            await loadFiles();
        } catch (error) {
            ElMessage.error(`删除 ${item.name} 失败: ${error.message}`);
        } finally {
            loading.value = false;
        }
    };

    return {
        items,
        currentPath,
        loading,
        pathParts,
        loadFiles,
        debouncedLoadFiles,
        navigateTo,
        navigateToFolder,
        navigateBack,
        navigateToRoot,
        navigateToPathByIndex,
        createFolder,
        downloadFile,
        deleteItem
    }
} 