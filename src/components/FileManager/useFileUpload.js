/**
 * @fileoverview 文件上传逻辑 Composable
 */
import { uploadQueue } from './uploadQueue'
import { uploadObject } from '../../services/obsService'

export function useFileUpload(loading, loadFiles, currentPath) {

    // 设置队列所需的回调和状态
    uploadQueue.setFileListComponent({
        loading: loading,
        loadFiles: loadFiles
    });

    /**
     * 处理文件上传事件
     * @param {Event} event - 文件输入框的change事件
     */
    const handleFileUpload = async (event) => {
        const files = event.target.files
        if (!files.length) return

        loading.value = true

        for (const file of files) {
            let objectKey = file.name;
            if (currentPath.value) {
                objectKey = currentPath.value.endsWith('/')
                    ? `${currentPath.value}${file.name}`
                    : `${currentPath.value}/${file.name}`;
            }
            if (objectKey.startsWith('/')) {
                objectKey = objectKey.substring(1);
            }

            // 创建上传任务，但不立即执行，而是添加到队列
            const uploadTask = () => uploadObject(objectKey, file, (progress) => {
                // console.log(`文件 ${file.name} 上传进度: ${progress}%`);
            }, {
                partSize: 20 * 1024 * 1024, // 20MB
                taskNum: 3,
                enableCheckpoint: true
            });

            uploadQueue.add(uploadTask, file, objectKey);
        }

        event.target.value = ''

        if (files.length === 0) {
            loading.value = false;
        }
    }

    return {
        handleFileUpload
    }
} 