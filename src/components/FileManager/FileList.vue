<template>
  <div class="space-y-4">
    <Breadcrumb
      :current-path="currentPath"
      :path-parts="pathParts"
      @navigate-back="navigateBack"
      @navigate-to-root="navigateToRoot"
      @navigate-to-path-by-index="navigateToPathByIndex"
    />
    <ActionBar
      :is-selecting="isSelecting"
      :selected-items-count="selectedItems.size"
      :loading="loading || isCopying"
      v-model:searchQuery="searchQuery"
      @toggle-select-mode="toggleSelectMode"
      @batch-delete="handleBatchDelete"
      @new-folder="showNewFolderModal = true"
      @file-uploaded="handleFileUpload"
      @copy="openCopyModalWithSelection"
    />
    <LoadingSpinner :loading="loading && !deleteProgress.show" />
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
      <div class="min-h-[400px]" role="region" aria-label="文件列表">
        <EmptyState v-if="!loading && filteredItems.length === 0" :is-search="!!searchQuery" />
        <ul v-else class="divide-y divide-gray-200 dark:divide-gray-700" role="list">
          <FileListItem
            v-for="item in filteredItems"
            :key="item.key"
            :item="item"
            :is-selecting="isSelecting"
            :is-selected="selectedItems.has(item)"
            @toggle-selection="toggleSelection"
            @navigate="navigateToFolder"
            @preview="previewFile"
            @download="downloadFile"
            @delete="deleteItem"
            @copy="openCopyModal"
          />
        </ul>
      </div>
    </div>
    <NewFolderModal
      :show="showNewFolderModal"
      @close="showNewFolderModal = false"
      @create-folder="handleCreateFolder"
    />
    <CopyModal
      :show="showCopyModal"
      :source-directory="selectedDirectory"
      :current-path="currentPath"
      @close="showCopyModal = false"
      @copy-success="loadFiles"
    />
    <BatchDeleteModal :progress="deleteProgress" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useFileManager } from './useFileManager'
import { useFileSelection } from './useFileSelection'
import { useFileUpload } from './useFileUpload'
import { useDirectoryCopy } from './useDirectoryCopy'
import { useBatchDelete } from './useBatchDelete'
import Breadcrumb from './Breadcrumb.vue'
import ActionBar from './ActionBar.vue'
import FileListItem from './FileListItem.vue'
import EmptyState from './EmptyState.vue'
import LoadingSpinner from './LoadingSpinner.vue'
import NewFolderModal from './modals/NewFolderModal.vue'
import CopyModal from './modals/CopyModal.vue'
import BatchDeleteModal from './modals/BatchDeleteModal.vue'
import { ElMessage } from 'element-plus'

const searchQuery = ref('')
const showNewFolderModal = ref(false)
const showCopyModal = ref(false)
const selectedDirectory = ref(null)

const {
  items, currentPath, loading, pathParts, loadFiles, navigateBack, navigateToRoot,
  navigateToPathByIndex, navigateToFolder, createFolder, downloadFile, deleteItem
} = useFileManager()

const {
  selectedItems, isSelecting, toggleSelection, toggleSelectMode, clearSelection
} = useFileSelection()

const { handleFileUpload } = useFileUpload(loading, loadFiles, currentPath)
const { isCopying } = useDirectoryCopy(currentPath, loadFiles)
const { deleteProgress, batchDelete } = useBatchDelete(loadFiles, clearSelection)

const filteredItems = computed(() => {
  if (!searchQuery.value) return items.value
  const query = searchQuery.value.toLowerCase()
  return items.value.filter(item => item.name.toLowerCase().includes(query))
})

onMounted(loadFiles)

const previewFile = (file) => {
  downloadFile(file);
}

const handleCreateFolder = async (folderName) => {
  const success = await createFolder(folderName)
  if (success) showNewFolderModal.value = false
}

const openCopyModal = (item) => {
  if (item.type === 'directory') {
    selectedDirectory.value = item
    showCopyModal.value = true
  }
}

const openCopyModalWithSelection = () => {
  const directories = Array.from(selectedItems.value).filter(i => i.type === 'directory');
  if (directories.length !== 1) {
    ElMessage.warning('请只选择一个目录进行复制。');
    return;
  }
  selectedDirectory.value = directories[0];
  showCopyModal.value = true;
};

const handleBatchDelete = () => {
  batchDelete(selectedItems)
}
</script>