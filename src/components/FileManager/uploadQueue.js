/**
 * @fileoverview 文件上传队列模块，用于控制并发上传。
 */
import { ElMessage } from 'element-plus'
import { nextTick } from 'vue'

class UploadQueue {
    constructor(maxConcurrent = 3) {
        this.queue = []
        this.active = 0
        this.maxConcurrent = maxConcurrent
        this.fileListComponent = null // 引用 FileList 组件实例或相关refs
    }

    add(task, file, objectKey) {
        this.queue.push({ task, file, objectKey })
        this.run()
    }

    async run() {
        if (this.active >= this.maxConcurrent || !this.queue.length) return
        this.active++
        const { task, file, objectKey } = this.queue.shift()

        try {
            const result = await task(); // task 已经是绑定了参数的 uploadObject 调用
            if (result.status === 'success') {
                ElMessage.success(`文件 ${file.name} 上传成功`);
            } else {
                ElMessage.error(`文件 ${file.name} 上传失败: ${result.error || '未知错误'}`);
                console.error(`上传文件 ${file.name} (${objectKey}) 失败:`, result);
            }
        } catch (error) {
            ElMessage.error(`上传文件 ${file.name} 时发生队列处理错误: ${error.message || '未知错误'}`);
            console.error(`队列处理 ${file.name} (${objectKey}) 异常:`, error);
        } finally {
            this.active--

            // 检查是否所有任务完成
            if (this.fileListComponent && this.queue.length === 0 && this.active === 0) {
                this.fileListComponent.loading.value = false;
                nextTick(() => {
                    this.fileListComponent.loadFiles().catch(error => {
                        console.error('上传完成后刷新文件列表失败:', error);
                    });
                });
            }

            setTimeout(() => this.run(), 0);
        }
    }

    setFileListComponent(componentInstance) {
        this.fileListComponent = componentInstance;
    }
}

export const uploadQueue = new UploadQueue(3); 