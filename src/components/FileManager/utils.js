/**
 * @fileoverview 通用辅助函数模块
 */

/**
 * 格式化文件大小
 * @param {number} bytes 文件大小（字节）
 * @returns {string} 格式化后的文件大小字符串
 */
export const formatFileSize = (bytes) => {
    if (!bytes) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return `${(bytes / Math.pow(k, i)).toFixed(2)} ${sizes[i]}`
}

/**
 * 防抖函数
 * @param {Function} func 需要防抖的函数
 * @param {number} wait 等待时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
export const debounce = (func, wait) => {
    let timeout

    return function executedFunction(...args) {
        return new Promise((resolve, reject) => {
            const later = async () => {
                try {
                    const result = await func(...args)
                    resolve(result)
                } catch (error) {
                    reject(error)
                }
            }

            clearTimeout(timeout)
            timeout = setTimeout(later, wait)
        })
    }
}

/**
 * 并发执行异步任务
 * @param {Array<Function>} tasks 任务函数数组，每个函数返回一个Promise
 * @param {number} maxConcurrent 最大并发数
 * @returns {Promise<void>}
 */
export const executeConcurrentTasks = async (tasks, maxConcurrent) => {
    const executing = [];
    let completedCount = 0;

    for (const task of tasks) {
        const promise = task().then(() => {
            completedCount++;
            executing.splice(executing.indexOf(promise), 1);
        }).catch(error => {
            completedCount++;
            console.error(`任务失败: ${completedCount}/${tasks.length}`, error);
            executing.splice(executing.indexOf(promise), 1);
        });

        executing.push(promise);

        if (executing.length >= maxConcurrent) {
            await Promise.race(executing);
        }
    }

    await Promise.all(executing);
} 