/**
 * @fileoverview 请求管理器模块，用于处理请求的缓存和防止重复请求。
 */

class RequestManager {
    constructor() {
        this.pendingRequests = new Map()
        this.requestCache = new Map()
        this.cacheTimeout = 5000 // 缓存5秒
    }

    async execute(key, requestFunction, ...args) {
        // 检查缓存
        const cached = this.requestCache.get(key)
        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            console.log(`返回缓存结果: ${key}`)
            return cached.data
        }

        // 如果已有相同的请求在进行中，返回该请求的Promise
        if (this.pendingRequests.has(key)) {
            console.log(`请求已在进行中，返回现有Promise: ${key}`)
            return this.pendingRequests.get(key)
        }

        // 创建新的请求Promise
        const requestPromise = new Promise(async (resolve, reject) => {
            try {
                console.log(`开始执行请求: ${key}`)
                const result = await requestFunction(...args)

                // 缓存结果
                this.requestCache.set(key, {
                    data: result,
                    timestamp: Date.now()
                })

                resolve(result)
            } catch (error) {
                console.error(`请求失败: ${key}`, error)
                reject(error)
            } finally {
                // 请求完成后清理
                this.pendingRequests.delete(key)
                console.log(`请求完成，清理记录: ${key}`)
            }
        })

        // 存储请求Promise
        this.pendingRequests.set(key, requestPromise)

        return requestPromise
    }

    // 取消特定请求
    cancel(key) {
        if (this.pendingRequests.has(key)) {
            this.pendingRequests.delete(key)
            console.log(`取消请求: ${key}`)
        }
    }

    // 清理所有请求和缓存
    clear() {
        this.pendingRequests.clear()
        this.requestCache.clear()
        console.log('清理所有请求记录和缓存')
    }

    // 清理过期缓存
    cleanExpiredCache() {
        const now = Date.now()
        for (const [key, value] of this.requestCache.entries()) {
            if (now - value.timestamp > this.cacheTimeout) {
                this.requestCache.delete(key)
            }
        }
    }
}

// 创建全局请求管理器实例
export const requestManager = new RequestManager()

// 定期清理过期缓存
const cacheCleanupInterval = setInterval(() => {
    requestManager.cleanExpiredCache();
}, 30000); // 每30秒清理一次

// 注意：在应用级别，你可能需要管理这个定时器的生命周期，
// 例如在应用卸载时 clearInterval(cacheCleanupInterval)。
// 在这个模块化场景下，我们假设它在应用生命周期内持续运行。 