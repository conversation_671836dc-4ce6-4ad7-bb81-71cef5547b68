/**
 * @fileoverview 批量删除逻辑 Composable
 */
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { batchDeleteFiles, deleteObjectsByPrefix } from '../../services/obsService'
import { executeConcurrentTasks } from './utils'

export function useBatchDelete(loadFiles, clearSelection) {
    const deleteProgress = ref({
        show: false,
        current: 0,
        total: 0,
        currentItem: '',
        details: [],
        startTime: null,
        speed: '',
        concurrent: '',
        elapsed: '',
        remaining: ''
    })

    const updatePerformanceStats = () => {
        if (!deleteProgress.value.startTime) {
            deleteProgress.value.startTime = Date.now()
            return
        }
        const now = Date.now()
        const elapsed = (now - deleteProgress.value.startTime) / 1000
        const processed = deleteProgress.value.current
        const total = deleteProgress.value.total
        if (elapsed > 0 && processed > 0) {
            const speed = processed / elapsed
            deleteProgress.value.speed = `${speed.toFixed(1)} 项/秒`
        }
        deleteProgress.value.elapsed = `${elapsed.toFixed(1)}s`
        if (processed > 0 && processed < total) {
            const remaining = ((total - processed) / (processed / elapsed))
            deleteProgress.value.remaining = `${remaining.toFixed(1)}s`
        } else if (processed >= total) {
            deleteProgress.value.remaining = '已完成'
        }
    }

    const batchDelete = async (selectedItems) => {
        if (selectedItems.size === 0) return;

        const confirmMessage = `确定要删除选中的 ${selectedItems.size} 个项目吗？此操作不可恢复！`;
        if (!confirm(confirmMessage)) return;

        deleteProgress.value = {
            show: true,
            current: 0,
            total: selectedItems.size,
            currentItem: '',
            details: [],
            startTime: Date.now(),
            speed: '计算中...',
            concurrent: '准备中',
            elapsed: '0s',
            remaining: '计算中...'
        }

        const filesToDeleteKeys = [];
        const directoriesToDelete = [];
        selectedItems.forEach(item => {
            if (item.type === 'directory') {
                directoriesToDelete.push(item);
            } else {
                filesToDeleteKeys.push(item.key);
            }
        });

        try {
            if (filesToDeleteKeys.length > 0) {
                deleteProgress.value.currentItem = `批量删除 ${filesToDeleteKeys.length} 个文件`;
                await batchDeleteFiles(filesToDeleteKeys, 1000, (processed, total) => {
                    deleteProgress.value.current = processed;
                    deleteProgress.value.currentItem = `批量删除文件: ${processed}/${total}`;
                    updatePerformanceStats();
                });
            }

            if (directoriesToDelete.length > 0) {
                const deleteDirectoryTasks = directoriesToDelete.map(item => async () => {
                    deleteProgress.value.currentItem = `删除目录: ${item.name}`;
                    await deleteObjectsByPrefix(item.key.endsWith('/') ? item.key : item.key + '/');
                    deleteProgress.value.current++;
                    updatePerformanceStats();
                });
                await executeConcurrentTasks(deleteDirectoryTasks, 3);
            }

            ElMessage.success(`批量删除成功！`);

        } catch (error) {
            ElMessage.error(`批量删除操作失败: ${error.message}`);
        } finally {
            clearSelection();
            setTimeout(() => {
                deleteProgress.value.show = false;
            }, 2000);
            await loadFiles();
        }
    }

    return {
        deleteProgress,
        batchDelete
    }
} 