/**
 * @fileoverview 文件选择逻辑 Composable
 */
import { ref } from 'vue'

export function useFileSelection() {
    const selectedItems = ref(new Set())
    const isSelecting = ref(false)

    /**
     * 切换单个项目的选中状态
     * @param {object} item - 文件或目录项
     */
    const toggleSelection = (item) => {
        const newSelectedItems = new Set(selectedItems.value)
        if (newSelectedItems.has(item)) {
            newSelectedItems.delete(item)
        } else {
            newSelectedItems.add(item)
        }
        selectedItems.value = newSelectedItems
    }

    /**
     * 切换多选模式
     */
    const toggleSelectMode = () => {
        isSelecting.value = !isSelecting.value
        if (!isSelecting.value) {
            selectedItems.value.clear()
        }
    }

    /**
     * 清除所有选中项
     */
    const clearSelection = () => {
        selectedItems.value.clear()
        isSelecting.value = false
    }

    return {
        selectedItems,
        isSelecting,
        toggleSelection,
        toggleSelectMode,
        clearSelection
    }
} 