<template>
  <TransitionRoot appear :show="show" as="template">
    <Dialog as="div" @close="closeModal" class="relative z-10">
      <TransitionChild
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black bg-opacity-25" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4">
          <TransitionChild
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel class="w-full max-w-md transform overflow-hidden rounded-2xl bg-white dark:bg-gray-800 p-6 text-left align-middle shadow-xl transition-all">
              <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900 dark:text-white">
                新建文件夹
              </DialogTitle>
              <div class="mt-4">
                <input
                  type="text"
                  class="input"
                  placeholder="输入文件夹名称"
                  v-model="folderName"
                  @keyup.enter="create"
                />
              </div>
              <div class="mt-4 flex justify-end space-x-2">
                <button class="btn-secondary" @click="closeModal">取消</button>
                <button class="btn-primary" @click="create" :disabled="!folderName.trim()">创建</button>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup>
import { ref } from 'vue'
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  TransitionRoot,
  TransitionChild,
} from '@headlessui/vue'
import { ElMessage } from 'element-plus'

defineProps({
  show: {
    type: Boolean,
    required: true
  }
})

const emit = defineEmits(['close', 'create-folder'])

const folderName = ref('')

const closeModal = () => {
  folderName.value = ''
  emit('close')
}

const create = () => {
  const trimmedName = folderName.value.trim()
  if (!trimmedName) {
    ElMessage.warning('文件夹名称不能为空。')
    return
  }
  emit('create-folder', trimmedName)
}
</script> 