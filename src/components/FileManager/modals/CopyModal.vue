<template>
  <TransitionRoot appear :show="show" as="template">
    <Dialog as="div" @close="closeModal" class="relative z-10">
      <TransitionChild
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black bg-opacity-25" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4">
          <DialogPanel class="w-full max-w-md transform overflow-hidden rounded-2xl bg-white dark:bg-gray-800 p-6 text-left align-middle shadow-xl transition-all">
            <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900 dark:text-white">
              复制目录内容
            </DialogTitle>
            <div class="mt-2">
              <p class="text-sm text-gray-500 dark:text-gray-400">
                将 "{{ sourceDirectory?.name }}" 目录下的文件复制到新目录
              </p>
            </div>
            <div class="mt-4">
              <input
                type="text"
                class="input"
                placeholder="输入新目录名称"
                v-model="targetFolderName"
                @keyup.enter="startCopy"
              />
            </div>
            <div class="mt-4 flex justify-end space-x-2">
              <button class="btn-secondary" @click="closeModal">取消</button>
              <button 
                class="btn-primary"
                @click="startCopy"
                :disabled="!targetFolderName.trim() || isCopying"
              >
                {{ isCopying ? '复制中...' : '复制' }}
              </button>
            </div>
          </DialogPanel>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup>
import { ref, watch, toRefs } from 'vue'
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  TransitionRoot,
  TransitionChild,
} from '@headlessui/vue'
import { ElMessage } from 'element-plus'
import { useDirectoryCopy } from '../useDirectoryCopy.js'

const props = defineProps({
  show: {
    type: Boolean,
    required: true
  },
  sourceDirectory: {
    type: Object,
    default: null
  },
  currentPath: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['close', 'copy-success'])
const { currentPath } = toRefs(props)
const { isCopying, copyDirectory } = useDirectoryCopy(currentPath, () => {
    emit('copy-success')
    emit('close')
})

const targetFolderName = ref('')

const closeModal = () => {
  if (isCopying.value) {
    // maybe ask for confirmation before cancelling
  }
  targetFolderName.value = ''
  emit('close')
}

const startCopy = async () => {
  const trimmedName = targetFolderName.value.trim()
  if (!trimmedName) {
    ElMessage.warning('目标目录名称不能为空。')
    return
  }
  if (!props.sourceDirectory) {
    ElMessage.error('源目录未指定。')
    return
  }

  const success = await copyDirectory(props.sourceDirectory, trimmedName)
  if (success) {
    closeModal()
  }
}

watch(() => props.show, (newVal) => {
  if (!newVal) {
    targetFolderName.value = ''
  }
})
</script> 