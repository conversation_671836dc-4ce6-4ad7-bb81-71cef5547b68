<template>
  <nav class="flex items-center space-x-2 text-sm">
    <div class="flex items-center space-x-2">
      <!-- 返回上一级按钮 -->
      <button 
        class="flex items-center px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
        @click="$emit('navigate-back')"
        :disabled="isRoot"
        :class="{'opacity-50 cursor-not-allowed': isRoot}"
        title="返回上一级"
      >
        <ArrowLeftIcon class="h-5 w-5 text-primary-600" />
      </button>
      <!-- 根目录按钮 -->
      <button 
        class="flex items-center px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
        @click="$emit('navigate-to-root')"
        :disabled="isRoot"
        :class="{'opacity-50 cursor-not-allowed': isRoot}"
        title="返回根目录"
      >
        <HomeIcon class="h-5 w-5 text-primary-600" />
      </button>
    </div>

    <!-- 路径导航 -->
    <div class="flex items-center space-x-2 text-gray-600 dark:text-gray-400" aria-label="文件路径导航">
      <button 
        class="px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
        @click="$emit('navigate-to-root')"
        aria-label="导航到根目录"
      >
        website_public
      </button>
      <template v-for="(part, index) in pathParts" :key="index">
        <ChevronRightIcon class="h-4 w-4" />
        <button 
          @click="$emit('navigate-to-path-by-index', index)"
          class="px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
        >
          {{ part }}
        </button>
      </template>
    </div>
  </nav>
</template>

<script setup>
import { computed } from 'vue'
import {
  ArrowLeftIcon,
  HomeIcon,
  ChevronRightIcon
} from '@heroicons/vue/24/outline'

const props = defineProps({
  currentPath: {
    type: String,
    required: true
  },
  pathParts: {
    type: Array,
    required: true
  }
})

defineEmits(['navigate-back', 'navigate-to-root', 'navigate-to-path-by-index'])

const isRoot = computed(() => props.currentPath === 'website_public')
</script> 