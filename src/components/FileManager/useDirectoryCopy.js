/**
 * @fileoverview 目录复制逻辑 Composable
 */
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { batchCopyObjectsRecursive, listObjectsInDirectory } from '../../services/obsService'
import { requestManager } from './requestManager'

export function useDirectoryCopy(currentPath, loadFiles) {
    const isCopying = ref(false)
    const currentCopyController = ref(null)
    // 在复制操作的生命周期内缓存目标目录的键
    const targetKeysCache = new Map();

    const getAllObjectKeysInPrefix = async (prefix, signal = null) => {
        // 如果缓存中已有数据，直接返回
        if (targetKeysCache.has(prefix)) {
            return targetKeysCache.get(prefix);
        }

        const existingKeys = new Set();
        let marker = null;
        let isTruncated = true;
        const normalizedPrefix = prefix.endsWith('/') ? prefix : prefix + '/';

        if (!normalizedPrefix || normalizedPrefix === '/') {
            return existingKeys;
        }

        try {
            while (isTruncated) {
                if (signal && signal.aborted) {
                    throw new Error('操作被取消')
                }
                const requestKey = `getAllObjects_${normalizedPrefix}_${marker || 'initial'}`
                const result = await requestManager.execute(requestKey, listObjectsInDirectory, normalizedPrefix, marker, 1000, signal)
                result.files.forEach(file => existingKeys.add(file.key));
                marker = result.nextMarker;
                isTruncated = !!marker;
            }
        } catch (error) {
            if (error.message !== '操作被取消') {
                console.error(`列出目标前缀 ${normalizedPrefix} 下对象时出错:`, error);
                ElMessage.error(`检查目标目录内容失败: ${error.message}`);
            }
            throw error
        }

        // 将结果存入缓存
        targetKeysCache.set(prefix, existingKeys);
        return existingKeys;
    }

    const copyDirectory = async (sourceDir, targetDirName) => {
        if (isCopying.value) {
            ElMessage.warning('复制操作正在进行中，请稍候...')
            return false
        }

        if (currentCopyController.value) {
            currentCopyController.value.abort()
        }

        isCopying.value = true
        currentCopyController.value = new AbortController()
        // 开始新的复制操作时，清空缓存
        targetKeysCache.clear();

        try {
            const sourcePath = `${currentPath.value}/${sourceDir.name}`
            const newFolderPath = `${currentPath.value}/${targetDirName}`

            if (`${sourcePath}/` === `${newFolderPath}/`) {
                ElMessage.error('源目录和目标目录不能相同。');
                return false
            }

            ElMessage.info('正在检查目标目录内容以进行增量复制...')
            const existingTargetKeys = await getAllObjectKeysInPrefix(newFolderPath, currentCopyController.value.signal)

            if (currentCopyController.value.signal.aborted) {
                ElMessage.info('复制操作已取消')
                return false
            }

            ElMessage.info(`开始复制目录 "${sourceDir.name}" 到 "${targetDirName}"...`)
            const result = await batchCopyObjectsRecursive(sourcePath, newFolderPath, existingTargetKeys, currentCopyController.value.signal)

            if (currentCopyController.value.signal.aborted) {
                ElMessage.info('复制操作已取消')
                return false
            }

            const successCount = result.filter(r => r.status === 'success').length;
            ElMessage.success(`目录复制完成，成功复制 ${successCount} 个文件。`);

            await loadFiles()
            return true
        } catch (error) {
            if (error.name !== 'AbortError' && !error.message.includes('取消')) {
                ElMessage.error(`目录复制失败: ${error.message}`);
            }
            return false
        } finally {
            isCopying.value = false
            currentCopyController.value = null
            // 操作结束后清空缓存
            targetKeysCache.clear();
        }
    }

    const cancelCopy = () => {
        if (currentCopyController.value) {
            currentCopyController.value.abort()
            ElMessage.info('复制操作已取消')
        }
    }

    return {
        isCopying,
        copyDirectory,
        cancelCopy
    }
} 