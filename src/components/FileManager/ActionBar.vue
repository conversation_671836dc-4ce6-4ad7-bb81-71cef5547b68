<template>
  <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
    <div class="flex flex-wrap gap-2">
      <button 
        class="btn-secondary flex items-center space-x-2" 
        @click="$emit('toggle-select-mode')"
      >
        <CheckCircleIcon class="h-5 w-5" :class="{'text-primary-600': isSelecting}" />
        <span>{{ isSelecting ? '取消多选' : '多选' }}</span>
      </button>
      <button 
        v-if="isSelecting"
        class="btn-danger flex items-center space-x-2" 
        @click="$emit('batch-delete')"
        :disabled="selectedItemsCount === 0"
        :class="{'opacity-50 cursor-not-allowed': selectedItemsCount === 0}"
      >
        <TrashIcon class="h-5 w-5" />
        <span>删除选中项 ({{ selectedItemsCount }})</span>
      </button>
      <button 
        class="btn-primary flex items-center space-x-2" 
        @click="$emit('new-folder')"
      >
        <FolderPlusIcon class="h-5 w-5" />
        <span>新建文件夹</span>
      </button>

      <label class="btn-primary flex items-center space-x-2 cursor-pointer">
        <ArrowUpTrayIcon class="h-5 w-5" />
        <span>上传文件</span>
        <input 
          type="file" 
          class="hidden" 
          @change="$emit('file-uploaded', $event)" 
          multiple 
          :disabled="loading"
        />
      </label>
      <button 
        v-if="isSelecting"
        class="btn-primary flex items-center space-x-2" 
        @click="$emit('copy')"
        :disabled="selectedItemsCount === 0"
        :class="{'opacity-50 cursor-not-allowed': selectedItemsCount === 0}"
      >
        <DocumentDuplicateIcon class="h-5 w-5" />
        <span>复制到新文件夹</span>
      </button>
    </div>
    <div class="relative">
      <input
        type="text"
        placeholder="搜索文件..."
        class="input pl-10"
        :value="searchQuery"
        @input="$emit('update:searchQuery', $event.target.value)"
      />
      <MagnifyingGlassIcon class="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
    </div>
  </div>
</template>

<script setup>
import {
  CheckCircleIcon,
  TrashIcon,
  FolderPlusIcon,
  ArrowUpTrayIcon,
  DocumentDuplicateIcon,
  MagnifyingGlassIcon
} from '@heroicons/vue/24/outline'

defineProps({
  isSelecting: {
    type: Boolean,
    required: true
  },
  selectedItemsCount: {
    type: Number,
    required: true
  },
  loading: {
    type: Boolean,
    required: true
  },
  searchQuery: {
    type: String,
    required: true
  }
})

defineEmits(['toggle-select-mode', 'batch-delete', 'new-folder', 'file-uploaded', 'copy', 'update:searchQuery'])
</script> 